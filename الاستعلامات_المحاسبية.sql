-- =====================================================
-- الاستعلامات المحاسبية لنظام إدارة التصنيع
-- نظام إدارة تصنيع المعمول بالجوز
-- =====================================================

-- =====================================================
-- استعلام حساب تكلفة المواد الخام لأمر إنتاج
-- =====================================================
CREATE VIEW استعلام_تكلفة_المواد_الخام AS
SELECT 
    أ.رقم_أمر_الإنتاج,
    أ.رقم_المنتج,
    م.اسم_المنتج,
    أ.الكمية_المطلوبة,
    SUM(ح.الكمية * ح.سعر_الوحدة) AS تكلفة_المواد_الخام,
    (SUM(ح.الكمية * ح.سعر_الوحدة) / أ.الكمية_المطلوبة) AS تكلفة_المواد_للوحدة
FROM أوامر_الإنتاج أ
INNER JOIN المنتجات_التامة م ON أ.رقم_المنتج = م.رقم_المنتج
INNER JOIN حركات_المخزون ح ON أ.رقم_أمر_الإنتاج = ح.رقم_المصدر 
    AND ح.مصدر_الحركة = 'أمر_إنتاج' 
    AND ح.نوع_الحركة = 'خروج'
GROUP BY أ.رقم_أمر_الإنتاج, أ.رقم_المنتج, م.اسم_المنتج, أ.الكمية_المطلوبة;

-- =====================================================
-- استعلام حساب إجمالي تكاليف أمر الإنتاج
-- =====================================================
CREATE VIEW استعلام_إجمالي_تكاليف_الإنتاج AS
SELECT 
    أ.رقم_أمر_الإنتاج,
    أ.رقم_المنتج,
    م.اسم_المنتج,
    أ.الكمية_المطلوبة,
    أ.الكمية_المنتجة_فعلياً,
    أ.تكلفة_المواد_الخام,
    أ.تكلفة_الأيدي_العاملة,
    أ.التكاليف_المباشرة,
    أ.التكاليف_غير_المباشرة,
    أ.إجمالي_التكلفة,
    أ.تكلفة_الوحدة,
    (أ.إجمالي_التكلفة / أ.الكمية_المنتجة_فعلياً) AS التكلفة_الفعلية_للوحدة,
    أ.حالة_الأمر,
    أ.تاريخ_الأمر
FROM أوامر_الإنتاج أ
INNER JOIN المنتجات_التامة م ON أ.رقم_المنتج = م.رقم_المنتج;

-- =====================================================
-- استعلام تقرير المخزون الحالي
-- =====================================================
CREATE VIEW تقرير_المخزون_الحالي AS
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    م.مجموعة_المادة,
    مخ.اسم_المخزن,
    ر.الكمية_المتاحة,
    ر.الكمية_المحجوزة,
    ر.الكمية_الصافية,
    ر.المتوسط_المرجح,
    ر.إجمالي_القيمة,
    و.اسم_الوحدة,
    م.الحد_الأدنى,
    م.نقطة_إعادة_الطلب,
    IIf(ر.الكمية_المتاحة <= م.الحد_الأدنى, "نعم", "لا") AS تحت_الحد_الأدنى,
    IIf(ر.الكمية_المتاحة <= م.نقطة_إعادة_الطلب, "نعم", "لا") AS يحتاج_إعادة_طلب,
    ر.تاريخ_آخر_حركة
FROM المواد_الخام م
INNER JOIN أرصدة_المخزون ر ON م.رقم_المادة = ر.رقم_المادة
INNER JOIN المخازن مخ ON ر.رقم_المخزن = مخ.رقم_المخزن
INNER JOIN وحدات_القياس و ON م.رقم_وحدة_القياس = و.رقم_الوحدة
WHERE ر.الكمية_المتاحة > 0;

-- =====================================================
-- استعلام حركات المخزون التفصيلية
-- =====================================================
CREATE VIEW تقرير_حركات_المخزون AS
SELECT 
    ح.رقم_الحركة,
    ح.تاريخ_الحركة,
    م.كود_المادة,
    م.اسم_المادة,
    مخ.اسم_المخزن,
    ح.نوع_الحركة,
    ح.مصدر_الحركة,
    ح.رقم_المصدر,
    ح.الكمية,
    ح.سعر_الوحدة,
    ح.إجمالي_القيمة,
    ح.الرصيد_قبل_الحركة,
    ح.الرصيد_بعد_الحركة,
    ح.المتوسط_المرجح_قبل,
    ح.المتوسط_المرجح_بعد,
    و.اسم_الوحدة,
    ح.ملاحظات,
    ح.مستخدم_الإدخال
FROM حركات_المخزون ح
INNER JOIN المواد_الخام م ON ح.رقم_المادة = م.رقم_المادة
INNER JOIN المخازن مخ ON ح.رقم_المخزن = مخ.رقم_المخزن
INNER JOIN وحدات_القياس و ON م.رقم_وحدة_القياس = و.رقم_الوحدة;

-- =====================================================
-- استعلام تحليل تكاليف الوصفات
-- =====================================================
CREATE VIEW تحليل_تكاليف_الوصفات AS
SELECT 
    و.رقم_الوصفة,
    و.اسم_الوصفة,
    م.اسم_المنتج,
    و.كمية_الإنتاج,
    SUM(ت.الكمية_المطلوبة * ر.المتوسط_المرجح) AS تكلفة_المواد_الخام,
    و.تكلفة_الأيدي_العاملة_للوحدة * و.كمية_الإنتاج AS تكلفة_الأيدي_العاملة,
    و.تكلفة_التشغيل_للوحدة * و.كمية_الإنتاج AS تكلفة_التشغيل,
    (SUM(ت.الكمية_المطلوبة * ر.المتوسط_المرجح) * و.نسبة_التكاليف_غير_المباشرة / 100) AS التكاليف_غير_المباشرة,
    (SUM(ت.الكمية_المطلوبة * ر.المتوسط_المرجح) + 
     (و.تكلفة_الأيدي_العاملة_للوحدة * و.كمية_الإنتاج) + 
     (و.تكلفة_التشغيل_للوحدة * و.كمية_الإنتاج) + 
     (SUM(ت.الكمية_المطلوبة * ر.المتوسط_المرجح) * و.نسبة_التكاليف_غير_المباشرة / 100)) AS إجمالي_التكلفة,
    ((SUM(ت.الكمية_المطلوبة * ر.المتوسط_المرجح) + 
      (و.تكلفة_الأيدي_العاملة_للوحدة * و.كمية_الإنتاج) + 
      (و.تكلفة_التشغيل_للوحدة * و.كمية_الإنتاج) + 
      (SUM(ت.الكمية_المطلوبة * ر.المتوسط_المرجح) * و.نسبة_التكاليف_غير_المباشرة / 100)) / و.كمية_الإنتاج) AS تكلفة_الوحدة
FROM الوصفات و
INNER JOIN المنتجات_التامة م ON و.رقم_المنتج = م.رقم_المنتج
INNER JOIN تفاصيل_الوصفات ت ON و.رقم_الوصفة = ت.رقم_الوصفة
INNER JOIN المواد_الخام مخ ON ت.رقم_المادة = مخ.رقم_المادة
LEFT JOIN أرصدة_المخزون ر ON ت.رقم_المادة = ر.رقم_المادة
WHERE و.نشطة = True
GROUP BY و.رقم_الوصفة, و.اسم_الوصفة, م.اسم_المنتج, و.كمية_الإنتاج, 
         و.تكلفة_الأيدي_العاملة_للوحدة, و.تكلفة_التشغيل_للوحدة, و.نسبة_التكاليف_غير_المباشرة;

-- =====================================================
-- استعلام تقرير المشتريات
-- =====================================================
CREATE VIEW تقرير_المشتريات AS
SELECT 
    ف.رقم_الفاتورة,
    ف.رقم_فاتورة_المورد,
    ف.تاريخ_الفاتورة,
    مو.اسم_المورد,
    مخ.اسم_المخزن,
    ف.إجمالي_الفاتورة,
    ف.قيمة_الضريبة,
    ف.إجمالي_شامل_الضريبة,
    ف.قيمة_الخصم,
    ف.الصافي_بعد_الخصم,
    ف.حالة_الفاتورة,
    COUNT(ت.رقم_التفصيل) AS عدد_الأصناف,
    SUM(ت.الكمية) AS إجمالي_الكمية
FROM فواتير_الشراء ف
INNER JOIN الموردين مو ON ف.رقم_المورد = مو.رقم_المورد
INNER JOIN المخازن مخ ON ف.رقم_المخزن = مخ.رقم_المخزن
LEFT JOIN تفاصيل_فواتير_الشراء ت ON ف.رقم_الفاتورة = ت.رقم_الفاتورة
GROUP BY ف.رقم_الفاتورة, ف.رقم_فاتورة_المورد, ف.تاريخ_الفاتورة, 
         مو.اسم_المورد, مخ.اسم_المخزن, ف.إجمالي_الفاتورة, 
         ف.قيمة_الضريبة, ف.إجمالي_شامل_الضريبة, ف.قيمة_الخصم, 
         ف.الصافي_بعد_الخصم, ف.حالة_الفاتورة;

-- =====================================================
-- استعلام تفاصيل المشتريات
-- =====================================================
CREATE VIEW تفاصيل_تقرير_المشتريات AS
SELECT 
    ف.رقم_الفاتورة,
    ف.تاريخ_الفاتورة,
    مو.اسم_المورد,
    م.كود_المادة,
    م.اسم_المادة,
    ت.الكمية,
    و.اسم_الوحدة,
    ت.سعر_الوحدة,
    ت.إجمالي_السطر,
    ت.نسبة_الخصم,
    ت.قيمة_الخصم,
    ت.الصافي_بعد_الخصم,
    ت.تاريخ_الصلاحية,
    ت.رقم_اللوط
FROM فواتير_الشراء ف
INNER JOIN الموردين مو ON ف.رقم_المورد = مو.رقم_المورد
INNER JOIN تفاصيل_فواتير_الشراء ت ON ف.رقم_الفاتورة = ت.رقم_الفاتورة
INNER JOIN المواد_الخام م ON ت.رقم_المادة = م.رقم_المادة
INNER JOIN وحدات_القياس و ON م.رقم_وحدة_القياس = و.رقم_الوحدة;

-- =====================================================
-- استعلام تقرير الإنتاج
-- =====================================================
CREATE VIEW تقرير_الإنتاج AS
SELECT 
    أ.رقم_أمر_الإنتاج,
    أ.تاريخ_الأمر,
    م.كود_المنتج,
    م.اسم_المنتج,
    و.اسم_الوصفة,
    أ.الكمية_المطلوبة,
    أ.الكمية_المنتجة_فعلياً,
    (أ.الكمية_المنتجة_فعلياً / أ.الكمية_المطلوبة * 100) AS نسبة_الإنجاز,
    أ.تاريخ_البدء_المخطط,
    أ.تاريخ_الانتهاء_المخطط,
    أ.تاريخ_البدء_الفعلي,
    أ.تاريخ_الانتهاء_الفعلي,
    مك.اسم_مركز_التكلفة,
    مخ.اسم_المخزن AS مخزن_الإنتاج,
    أ.حالة_الأمر,
    أ.تكلفة_المواد_الخام,
    أ.تكلفة_الأيدي_العاملة,
    أ.التكاليف_المباشرة,
    أ.التكاليف_غير_المباشرة,
    أ.إجمالي_التكلفة,
    أ.تكلفة_الوحدة
FROM أوامر_الإنتاج أ
INNER JOIN المنتجات_التامة م ON أ.رقم_المنتج = م.رقم_المنتج
INNER JOIN الوصفات و ON أ.رقم_الوصفة = و.رقم_الوصفة
LEFT JOIN مراكز_التكلفة مك ON أ.رقم_مركز_التكلفة = مك.رقم_مركز_التكلفة
INNER JOIN المخازن مخ ON أ.رقم_المخزن_الإنتاج = مخ.رقم_المخزن;

-- =====================================================
-- استعلام المواد تحت الحد الأدنى
-- =====================================================
CREATE VIEW المواد_تحت_الحد_الأدنى AS
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    م.مجموعة_المادة,
    مخ.اسم_المخزن,
    ر.الكمية_المتاحة,
    م.الحد_الأدنى,
    م.نقطة_إعادة_الطلب,
    (م.الحد_الأدنى - ر.الكمية_المتاحة) AS الكمية_المطلوبة,
    و.اسم_الوحدة,
    مو.اسم_المورد AS المورد_الافتراضي,
    م.سعر_الشراء_الافتراضي,
    ر.تاريخ_آخر_حركة
FROM المواد_الخام م
INNER JOIN أرصدة_المخزون ر ON م.رقم_المادة = ر.رقم_المادة
INNER JOIN المخازن مخ ON ر.رقم_المخزن = مخ.رقم_المخزن
INNER JOIN وحدات_القياس و ON م.رقم_وحدة_القياس = و.رقم_الوحدة
LEFT JOIN الموردين مو ON م.رقم_المورد_الافتراضي = مو.رقم_المورد
WHERE ر.الكمية_المتاحة <= م.الحد_الأدنى
ORDER BY م.مجموعة_المادة, م.اسم_المادة;
