# إنشاء نظام إدارة تصنيع المعمول الاحترافي الكامل
Write-Host "🚀 بدء إنشاء النظام الاحترافي الكامل..." -ForegroundColor Green

$dbPath = "نظام_المعمول_الاحترافي.accdb"

# حذف قاعدة البيانات القديمة
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force
    Write-Host "تم حذف قاعدة البيانات القديمة" -ForegroundColor Yellow
}

try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    $access.NewCurrentDatabase($dbPath)
    
    Write-Host "✅ تم إنشاء قاعدة البيانات: $dbPath" -ForegroundColor Green
    
    # ==================== إنشاء الجداول الأساسية ====================
    Write-Host "📋 إنشاء الجداول الأساسية..." -ForegroundColor Cyan
    
    # 1. جدول وحدات القياس
    $access.DoCmd.RunSQL("CREATE TABLE وحدات_القياس (
        رقم_الوحدة COUNTER PRIMARY KEY,
        اسم_الوحدة TEXT(50) NOT NULL,
        رمز_الوحدة TEXT(10) NOT NULL,
        نوع_الوحدة TEXT(20),
        معامل_التحويل DOUBLE DEFAULT 1,
        وحدة_أساسية YESNO DEFAULT False,
        ملاحظات MEMO,
        نشطة YESNO DEFAULT True,
        تاريخ_الإنشاء DATETIME DEFAULT Now(),
        مستخدم_الإنشاء TEXT(50) DEFAULT 'النظام'
    )")
    
    # 2. جدول المخازن
    $access.DoCmd.RunSQL("CREATE TABLE المخازن (
        رقم_المخزن COUNTER PRIMARY KEY,
        كود_المخزن TEXT(20) NOT NULL,
        اسم_المخزن TEXT(100) NOT NULL,
        نوع_المخزن TEXT(50),
        موقع_المخزن TEXT(200),
        مسؤول_المخزن TEXT(100),
        سعة_المخزن DOUBLE,
        درجة_الحرارة_المطلوبة DOUBLE,
        نسبة_الرطوبة_المطلوبة DOUBLE,
        ملاحظات MEMO,
        نشط YESNO DEFAULT True,
        تاريخ_الإنشاء DATETIME DEFAULT Now()
    )")
    
    # 3. جدول مراكز التكلفة
    $access.DoCmd.RunSQL("CREATE TABLE مراكز_التكلفة (
        رقم_مركز_التكلفة COUNTER PRIMARY KEY,
        كود_مركز_التكلفة TEXT(20) NOT NULL,
        اسم_مركز_التكلفة TEXT(100) NOT NULL,
        نوع_مركز_التكلفة TEXT(50),
        مسؤول_المركز TEXT(100),
        معدل_التحميل_بالساعة CURRENCY,
        معدل_التحميل_بالوحدة CURRENCY,
        نشط YESNO DEFAULT True,
        تاريخ_الإنشاء DATETIME DEFAULT Now()
    )")
    
    # 4. جدول الموردين
    $access.DoCmd.RunSQL("CREATE TABLE الموردين (
        رقم_المورد COUNTER PRIMARY KEY,
        كود_المورد TEXT(20) NOT NULL,
        اسم_المورد TEXT(100) NOT NULL,
        اسم_جهة_الاتصال TEXT(100),
        عنوان_المورد MEMO,
        هاتف_المورد TEXT(50),
        فاكس_المورد TEXT(50),
        ايميل_المورد TEXT(100),
        موقع_الويب TEXT(200),
        شروط_الدفع TEXT(100),
        حد_الائتمان CURRENCY DEFAULT 0,
        فترة_السداد INTEGER DEFAULT 30,
        تقييم_المورد INTEGER DEFAULT 5,
        رقم_السجل_التجاري TEXT(50),
        الرقم_الضريبي TEXT(50),
        ملاحظات MEMO,
        نشط YESNO DEFAULT True,
        تاريخ_الإنشاء DATETIME DEFAULT Now()
    )")
    
    # 5. جدول العملاء
    $access.DoCmd.RunSQL("CREATE TABLE العملاء (
        رقم_العميل COUNTER PRIMARY KEY,
        كود_العميل TEXT(20) NOT NULL,
        اسم_العميل TEXT(100) NOT NULL,
        نوع_العميل TEXT(20) DEFAULT 'تجزئة',
        اسم_جهة_الاتصال TEXT(100),
        عنوان_العميل MEMO,
        هاتف_العميل TEXT(50),
        ايميل_العميل TEXT(100),
        حد_الائتمان CURRENCY DEFAULT 0,
        فترة_السداد INTEGER DEFAULT 0,
        نسبة_الخصم DOUBLE DEFAULT 0,
        تصنيف_العميل TEXT(20) DEFAULT 'عادي',
        ملاحظات MEMO,
        نشط YESNO DEFAULT True,
        تاريخ_الإنشاء DATETIME DEFAULT Now()
    )")
    
    # 6. جدول المواد الخام
    $access.DoCmd.RunSQL("CREATE TABLE المواد_الخام (
        رقم_المادة COUNTER PRIMARY KEY,
        كود_المادة TEXT(50) NOT NULL,
        اسم_المادة TEXT(100) NOT NULL,
        الاسم_الإنجليزي TEXT(100),
        مجموعة_المادة TEXT(50),
        رقم_وحدة_القياس LONG,
        رقم_المورد_الافتراضي LONG,
        الحد_الأدنى DOUBLE DEFAULT 0,
        الحد_الأقصى DOUBLE DEFAULT 0,
        نقطة_إعادة_الطلب DOUBLE DEFAULT 0,
        الكمية_الاقتصادية DOUBLE DEFAULT 0,
        متوسط_فترة_التوريد INTEGER DEFAULT 7,
        التكلفة_المعيارية CURRENCY DEFAULT 0,
        آخر_سعر_شراء CURRENCY DEFAULT 0,
        تاريخ_آخر_شراء DATETIME,
        مدة_الصلاحية INTEGER DEFAULT 365,
        درجة_حرارة_التخزين DOUBLE,
        نسبة_رطوبة_التخزين DOUBLE,
        خصائص_خاصة MEMO,
        ملاحظات MEMO,
        نشط YESNO DEFAULT True,
        تاريخ_الإنشاء DATETIME DEFAULT Now()
    )")
    
    # 7. جدول المنتجات التامة
    $access.DoCmd.RunSQL("CREATE TABLE المنتجات_التامة (
        رقم_المنتج COUNTER PRIMARY KEY,
        كود_المنتج TEXT(50) NOT NULL,
        اسم_المنتج TEXT(100) NOT NULL,
        الاسم_الإنجليزي TEXT(100),
        مجموعة_المنتج TEXT(50),
        رقم_وحدة_القياس LONG,
        الوزن_الصافي DOUBLE,
        الوزن_الإجمالي DOUBLE,
        الأبعاد TEXT(50),
        سعر_البيع_التجزئة CURRENCY DEFAULT 0,
        سعر_البيع_الجملة CURRENCY DEFAULT 0,
        التكلفة_المعيارية CURRENCY DEFAULT 0,
        هامش_الربح_المستهدف DOUBLE DEFAULT 0,
        مدة_الصلاحية INTEGER DEFAULT 30,
        درجة_حرارة_التخزين DOUBLE,
        طريقة_التعبئة TEXT(100),
        معلومات_غذائية MEMO,
        ملاحظات MEMO,
        نشط YESNO DEFAULT True,
        تاريخ_الإنشاء DATETIME DEFAULT Now()
    )")
    
    # 8. جدول الوصفات
    $access.DoCmd.RunSQL("CREATE TABLE الوصفات (
        رقم_الوصفة COUNTER PRIMARY KEY,
        كود_الوصفة TEXT(50) NOT NULL,
        اسم_الوصفة TEXT(100) NOT NULL,
        رقم_المنتج LONG NOT NULL,
        كمية_الإنتاج DOUBLE NOT NULL,
        رقم_وحدة_الإنتاج LONG,
        زمن_التحضير INTEGER DEFAULT 0,
        زمن_الطبخ INTEGER DEFAULT 0,
        زمن_التبريد INTEGER DEFAULT 0,
        إجمالي_الوقت INTEGER DEFAULT 0,
        درجة_حرارة_الطبخ DOUBLE,
        تعليمات_التحضير MEMO,
        ملاحظات_خاصة MEMO,
        تكلفة_المواد_الخام CURRENCY DEFAULT 0,
        تكلفة_العمالة CURRENCY DEFAULT 0,
        التكلفة_الإجمالية CURRENCY DEFAULT 0,
        تكلفة_الوحدة CURRENCY DEFAULT 0,
        نشطة YESNO DEFAULT True,
        تاريخ_الإنشاء DATETIME DEFAULT Now(),
        تاريخ_آخر_تحديث DATETIME DEFAULT Now()
    )")
    
    # 9. جدول تفاصيل الوصفات
    $access.DoCmd.RunSQL("CREATE TABLE تفاصيل_الوصفات (
        رقم_التفصيل COUNTER PRIMARY KEY,
        رقم_الوصفة LONG NOT NULL,
        رقم_المادة LONG NOT NULL,
        الكمية_المطلوبة DOUBLE NOT NULL,
        رقم_وحدة_القياس LONG,
        نسبة_الفاقد DOUBLE DEFAULT 0,
        الكمية_الصافية DOUBLE,
        التكلفة_المعيارية CURRENCY DEFAULT 0,
        إجمالي_التكلفة CURRENCY DEFAULT 0,
        ترتيب_الإضافة INTEGER DEFAULT 1,
        ملاحظات_خاصة MEMO,
        إجباري YESNO DEFAULT True
    )")
    
    Write-Host "✅ تم إنشاء الجداول الأساسية (9 جداول)" -ForegroundColor Green
    
    # ==================== إنشاء جداول المعاملات ====================
    Write-Host "📋 إنشاء جداول المعاملات..." -ForegroundColor Cyan
    
    # 10. جدول فواتير الشراء
    $access.DoCmd.RunSQL("CREATE TABLE فواتير_الشراء (
        رقم_الفاتورة COUNTER PRIMARY KEY,
        رقم_فاتورة_المورد TEXT(50) NOT NULL,
        رقم_المورد LONG NOT NULL,
        تاريخ_الفاتورة DATETIME NOT NULL,
        تاريخ_الاستحقاق DATETIME,
        تاريخ_التوريد DATETIME,
        رقم_المخزن LONG NOT NULL,
        رقم_أمر_الشراء LONG,
        إجمالي_الفاتورة CURRENCY DEFAULT 0,
        نسبة_الخصم DOUBLE DEFAULT 0,
        قيمة_الخصم CURRENCY DEFAULT 0,
        نسبة_الضريبة DOUBLE DEFAULT 14,
        قيمة_الضريبة CURRENCY DEFAULT 0,
        الصافي_بعد_الخصم CURRENCY DEFAULT 0,
        إجمالي_شامل_الضريبة CURRENCY DEFAULT 0,
        مصاريف_الشحن CURRENCY DEFAULT 0,
        مصاريف_أخرى CURRENCY DEFAULT 0,
        الإجمالي_النهائي CURRENCY DEFAULT 0,
        حالة_الفاتورة TEXT(20) DEFAULT 'مسودة',
        حالة_السداد TEXT(20) DEFAULT 'غير مسددة',
        المبلغ_المسدد CURRENCY DEFAULT 0,
        المبلغ_المتبقي CURRENCY DEFAULT 0,
        ملاحظات MEMO,
        مستخدم_الإدخال TEXT(50),
        تاريخ_الإدخال DATETIME DEFAULT Now(),
        مستخدم_التعديل TEXT(50),
        تاريخ_التعديل DATETIME
    )")
    
    # 11. جدول تفاصيل فواتير الشراء
    $access.DoCmd.RunSQL("CREATE TABLE تفاصيل_فواتير_الشراء (
        رقم_التفصيل COUNTER PRIMARY KEY,
        رقم_الفاتورة LONG NOT NULL,
        رقم_المادة LONG NOT NULL,
        الكمية DOUBLE NOT NULL,
        سعر_الوحدة CURRENCY NOT NULL,
        إجمالي_السطر CURRENCY,
        نسبة_الخصم DOUBLE DEFAULT 0,
        قيمة_الخصم CURRENCY DEFAULT 0,
        الصافي_بعد_الخصم CURRENCY,
        تاريخ_الصلاحية DATETIME,
        رقم_اللوط TEXT(50),
        بلد_المنشأ TEXT(50),
        رقم_الشهادة TEXT(50),
        ملاحظات MEMO
    )")
    
    Write-Host "✅ تم إنشاء جداول المعاملات الأساسية" -ForegroundColor Green
    
    # ==================== إدخال البيانات الأساسية ====================
    Write-Host "📊 إدخال البيانات الأساسية..." -ForegroundColor Cyan
    
    # وحدات القياس
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, وحدة_أساسية) VALUES ('كيلو جرام', 'كجم', 'وزن', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, معامل_التحويل) VALUES ('جرام', 'جم', 'وزن', 0.001)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, وحدة_أساسية) VALUES ('لتر', 'لتر', 'حجم', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, معامل_التحويل) VALUES ('مليلتر', 'مل', 'حجم', 0.001)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, وحدة_أساسية) VALUES ('قطعة', 'قطعة', 'عدد', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, وحدة_أساسية) VALUES ('عبوة', 'عبوة', 'تعبئة', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, وحدة_أساسية) VALUES ('كيس', 'كيس', 'تعبئة', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, وحدة_أساسية) VALUES ('صندوق', 'صندوق', 'تعبئة', True)")
    
    # المخازن
    $access.DoCmd.RunSQL("INSERT INTO المخازن (كود_المخزن, اسم_المخزن, نوع_المخزن, موقع_المخزن, سعة_المخزن, درجة_الحرارة_المطلوبة) VALUES ('WH-RM', 'مخزن المواد الخام', 'مواد خام', 'الدور الأرضي - قسم أ', 1000, 25)")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (كود_المخزن, اسم_المخزن, نوع_المخزن, موقع_المخزن, سعة_المخزن, درجة_الحرارة_المطلوبة) VALUES ('WH-FG', 'مخزن المنتجات التامة', 'منتجات تامة', 'الدور الأرضي - قسم ب', 500, 18)")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (كود_المخزن, اسم_المخزن, نوع_المخزن, موقع_المخزن, سعة_المخزن, درجة_الحرارة_المطلوبة) VALUES ('WH-PR', 'مخزن الإنتاج', 'إنتاج', 'الدور الأول', 200, 22)")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (كود_المخزن, اسم_المخزن, نوع_المخزن, موقع_المخزن, سعة_المخزن, درجة_الحرارة_المطلوبة) VALUES ('WH-QC', 'مخزن الحجر الصحي', 'حجر صحي', 'الدور الأرضي - قسم ج', 100, 20)")
    
    # مراكز التكلفة
    $access.DoCmd.RunSQL("INSERT INTO مراكز_التكلفة (كود_مركز_التكلفة, اسم_مركز_التكلفة, نوع_مركز_التكلفة, معدل_التحميل_بالساعة) VALUES ('CC-MIX', 'مركز الخلط والتحضير', 'إنتاجي', 50)")
    $access.DoCmd.RunSQL("INSERT INTO مراكز_التكلفة (كود_مركز_التكلفة, اسم_مركز_التكلفة, نوع_مركز_التكلفة, معدل_التحميل_بالساعة) VALUES ('CC-SHP', 'مركز التشكيل', 'إنتاجي', 45)")
    $access.DoCmd.RunSQL("INSERT INTO مراكز_التكلفة (كود_مركز_التكلفة, اسم_مركز_التكلفة, نوع_مركز_التكلفة, معدل_التحميل_بالساعة) VALUES ('CC-BAK', 'مركز الخبز', 'إنتاجي', 60)")
    $access.DoCmd.RunSQL("INSERT INTO مراكز_التكلفة (كود_مركز_التكلفة, اسم_مركز_التكلفة, نوع_مركز_التكلفة, معدل_التحميل_بالساعة) VALUES ('CC-PCK', 'مركز التعبئة والتغليف', 'إنتاجي', 35)")
    $access.DoCmd.RunSQL("INSERT INTO مراكز_التكلفة (كود_مركز_التكلفة, اسم_مركز_التكلفة, نوع_مركز_التكلفة, معدل_التحميل_بالساعة) VALUES ('CC-QC', 'مركز مراقبة الجودة', 'خدمي', 40)")
    $access.DoCmd.RunSQL("INSERT INTO مراكز_التكلفة (كود_مركز_التكلفة, اسم_مركز_التكلفة, نوع_مركز_التكلفة, معدل_التحميل_بالساعة) VALUES ('CC-ADM', 'مركز الإدارة العامة', 'إداري', 30)")
    
    Write-Host "✅ تم إدخال البيانات الأساسية" -ForegroundColor Green
    
    # حفظ التقدم
    $access.CloseCurrentDatabase()
    $access.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    
    Write-Host "🎉 تم إنشاء المرحلة الأولى بنجاح!" -ForegroundColor Green
    Write-Host "📁 الملف: $dbPath" -ForegroundColor Yellow
    Write-Host "📊 الجداول المُنشأة: 11 جدول أساسي" -ForegroundColor White
    Write-Host "📈 البيانات المُدخلة: وحدات القياس، مخازن، مراكز تكلفة" -ForegroundColor White
    
} catch {
    Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
    if ($access) {
        try {
            $access.CloseCurrentDatabase()
            $access.Quit()
        } catch {}
    }
}

Write-Host "`n🔄 المرحلة الأولى مكتملة - جاري الانتقال للمرحلة الثانية..." -ForegroundColor Cyan
