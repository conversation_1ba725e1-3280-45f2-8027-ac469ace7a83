# دليل المستخدم - نظام إدارة تصنيع المعمول بالجوز

## نظرة عامة على النظام

نظام إدارة تصنيع المعمول بالجوز هو نظام شامل مصمم خصيصاً لإدارة عمليات التصنيع والمخزون والتكاليف في مصانع المعمول. يوفر النظام إدارة متقدمة للمخزون باستخدام طريقة المتوسط المرجح مع تحديث تلقائي للأرصدة والقيم.

## المتطلبات التقنية

- **نظام التشغيل**: Windows 10 أو أحدث
- **البرنامج المطلوب**: Microsoft Access 2016 أو أحدث
- **الذاكرة**: 4 جيجابايت RAM كحد أدنى
- **مساحة القرص**: 500 ميجابايت مساحة فارغة
- **الشاشة**: دقة 1024x768 كحد أدنى

## تثبيت النظام

### الطريقة الأولى: التثبيت التلقائي
1. تأكد من تثبيت Microsoft Access على جهازك
2. قم بتشغيل ملف `إنشاء_قاعدة_البيانات.ps1` كمدير
3. اتبع التعليمات التي تظهر على الشاشة
4. سيتم إنشاء قاعدة البيانات تلقائياً مع جميع الجداول والنماذج

### الطريقة الثانية: التثبيت اليدوي
1. افتح Microsoft Access
2. أنشئ قاعدة بيانات جديدة باسم "نظام_إدارة_تصنيع_المعمول.accdb"
3. استورد الجداول من ملف SQL المرفق
4. أنشئ النماذج والتقارير حسب التصميم المرفق

## الشاشة الرئيسية

عند فتح النظام، ستظهر الشاشة الرئيسية التي تحتوي على:

- **أزرار التنقل الرئيسية**: للوصول السريع لجميع أقسام النظام
- **لوحة المعلومات**: عرض إحصائيات سريعة عن المخزون والإنتاج
- **التنبيهات**: تنبيهات المواد تحت الحد الأدنى وأوامر الإنتاج المتأخرة
- **معلومات النظام**: تاريخ اليوم ومعلومات المستخدم الحالي

## إدارة البيانات الأساسية

### 1. إدارة وحدات القياس
- انتقل إلى **البيانات الأساسية** > **وحدات القياس**
- أضف الوحدات المطلوبة (كيلو، جرام، لتر، قطعة، إلخ)
- تأكد من إدخال رمز مختصر لكل وحدة

### 2. إدارة المخازن
- انتقل إلى **البيانات الأساسية** > **المخازن**
- أضف المخازن المختلفة (مخزن المواد الخام، المنتجات التامة، إلخ)
- حدد مسؤول لكل مخزن مع بيانات الاتصال

### 3. إدارة الموردين
- انتقل إلى **البيانات الأساسية** > **الموردين**
- أدخل بيانات الموردين كاملة
- حدد شروط الدفع وحد الائتمان لكل مورد

### 4. إدارة المواد الخام
- انتقل إلى **البيانات الأساسية** > **المواد الخام**
- أدخل بيانات كل مادة خام:
  - كود المادة (فريد)
  - اسم المادة
  - وحدة القياس
  - الحد الأدنى ونقطة إعادة الطلب
  - المورد الافتراضي
  - مجموعة المادة

### 5. إدارة المنتجات التامة
- انتقل إلى **البيانات الأساسية** > **المنتجات التامة**
- أدخل بيانات المنتجات:
  - كود المنتج (فريد)
  - اسم المنتج
  - الوزن الصافي
  - مدة الصلاحية
  - سعر البيع المقترح

## إدارة الوصفات

### إنشاء وصفة جديدة
1. انتقل إلى **الإنتاج** > **الوصفات**
2. اختر المنتج المراد إنشاء وصفة له
3. حدد كمية الإنتاج (مثلاً 100 قطعة)
4. أضف المواد الخام المطلوبة:
   - اختر المادة الخام
   - حدد الكمية المطلوبة
   - حدد وحدة القياس
   - أدخل نسبة الفاقد المتوقع
5. حدد تكاليف الأيدي العاملة والتشغيل
6. حدد نسبة التكاليف غير المباشرة
7. احفظ الوصفة وفعلها

### تعديل الوصفات
- يمكن تعديل الوصفات الموجودة
- عند التعديل، يتم إنشاء إصدار جديد
- الوصفات القديمة تبقى للمرجعية

## عمليات الشراء

### إنشاء فاتورة شراء
1. انتقل إلى **المشتريات** > **فواتير الشراء**
2. أنشئ فاتورة جديدة:
   - اختر المورد
   - حدد تاريخ الفاتورة
   - اختر المخزن المستقبل
3. أضف تفاصيل الفاتورة:
   - اختر المادة الخام
   - أدخل الكمية
   - أدخل سعر الوحدة
   - حدد تاريخ الصلاحية ورقم اللوط
4. احفظ الفاتورة كمسودة
5. راجع البيانات واعتمد الفاتورة
6. اضغط **معالجة المخزون** لتحديث الأرصدة

### معالجة المخزون
عند اعتماد فاتورة الشراء:
- يتم إضافة الكميات للمخزون تلقائياً
- يتم حساب المتوسط المرجح الجديد
- يتم تحديث قيمة المخزون
- يتم تسجيل حركة دخول في سجل الحركات

## عمليات الإنتاج

### إنشاء أمر إنتاج
1. انتقل إلى **الإنتاج** > **أوامر الإنتاج**
2. أنشئ أمر إنتاج جديد:
   - اختر المنتج
   - اختر الوصفة
   - حدد الكمية المطلوبة
   - حدد تواريخ البدء والانتهاء المخططة
   - اختر مركز التكلفة
   - حدد مخزن الإنتاج
3. احفظ الأمر بحالة "مخطط"

### تنفيذ أمر الإنتاج
1. غير حالة الأمر إلى "قيد التنفيذ"
2. اضغط **صرف المواد الخام**:
   - يتم حساب الكميات المطلوبة تلقائياً حسب الوصفة
   - يتم صرف المواد من المخزون
   - يتم تحديث الأرصدة والمتوسط المرجح
3. أدخل تكاليف الأيدي العاملة:
   - اسم العامل
   - نوع العمل
   - عدد الساعات
   - معدل الساعة
4. أدخل التكاليف المباشرة (كهرباء، غاز، إلخ)
5. يتم حساب التكاليف غير المباشرة تلقائياً

### إنهاء أمر الإنتاج
1. أدخل الكمية المنتجة فعلياً
2. غير حالة الأمر إلى "مكتمل"
3. يتم حساب التكلفة النهائية تلقائياً:
   - تكلفة المواد الخام
   - تكلفة الأيدي العاملة
   - التكاليف المباشرة
   - التكاليف غير المباشرة
   - إجمالي التكلفة
   - تكلفة الوحدة

## التقارير

### تقرير المخزون الحالي
- يعرض أرصدة جميع المواد في المخازن
- يظهر المواد تحت الحد الأدنى
- يعرض قيم المخزون بالمتوسط المرجح
- يمكن فلترته حسب المخزن أو مجموعة المواد

### تقرير حركات المخزون
- يعرض جميع حركات الدخول والخروج
- يمكن فلترته حسب المادة أو المخزن أو التاريخ
- يظهر تأثير كل حركة على الرصيد والمتوسط المرجح

### تقرير التكاليف
- يعرض تكاليف الإنتاج التفصيلية
- يقارن التكلفة المخططة مع الفعلية
- يحلل التكاليف حسب نوعها
- يحسب هامش الربح المتوقع

### تقرير الإنتاج
- يعرض أوامر الإنتاج وحالتها
- يظهر نسب الإنجاز
- يقارن الكميات المخططة مع المنتجة
- يحلل الأداء الإنتاجي

## نصائح مهمة

### إدارة المخزون
- تأكد من إدخال فواتير الشراء بانتظام
- راجع تقرير المواد تحت الحد الأدنى دورياً
- احرص على دقة بيانات تواريخ الصلاحية
- قم بجرد دوري للمخزون

### إدارة التكاليف
- أدخل تكاليف الأيدي العاملة بدقة
- سجل جميع التكاليف المباشرة
- راجع نسب التكاليف غير المباشرة دورياً
- قارن التكاليف الفعلية مع المخططة

### النسخ الاحتياطي
- قم بعمل نسخة احتياطية يومياً
- احفظ النسخ في مكان آمن
- اختبر استعادة النسخ الاحتياطية دورياً

### الأمان
- استخدم كلمات مرور قوية
- حدد صلاحيات مناسبة لكل مستخدم
- راجع سجلات العمليات دورياً
- لا تشارك بيانات الدخول

## الدعم الفني

في حالة مواجهة أي مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من رسائل الخطأ وحاول فهمها
3. تأكد من صحة البيانات المدخلة
4. قم بإعادة تشغيل البرنامج
5. تواصل مع الدعم الفني إذا لزم الأمر

## تحديثات النظام

- يتم إصدار تحديثات دورية للنظام
- احرص على تطبيق التحديثات الأمنية
- اقرأ ملاحظات الإصدار قبل التحديث
- قم بعمل نسخة احتياطية قبل أي تحديث
