# إنشاء نظام إدارة تصنيع المعمول النهائي الكامل
Write-Host "🚀 إنشاء النظام النهائي الكامل..." -ForegroundColor Green

$dbPath = "نظام_المعمول_النهائي.accdb"

# حذف الملف إذا كان موجوداً
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force
    Write-Host "تم حذف الملف القديم" -ForegroundColor Yellow
}

try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    $access.NewCurrentDatabase($dbPath)
    
    Write-Host "✅ تم إنشاء قاعدة البيانات: $dbPath" -ForegroundColor Green
    
    # ==================== إنشاء الجداول ====================
    Write-Host "📋 إنشاء الجداول..." -ForegroundColor Cyan
    
    # 1. وحدات القياس
    $access.DoCmd.RunSQL("CREATE TABLE وحدات_القياس (
        رقم_الوحدة COUNTER PRIMARY KEY,
        اسم_الوحدة TEXT(50),
        رمز_الوحدة TEXT(10),
        نوع_الوحدة TEXT(20),
        معامل_التحويل DOUBLE,
        وحدة_أساسية YESNO,
        نشطة YESNO
    )")
    
    # 2. المخازن
    $access.DoCmd.RunSQL("CREATE TABLE المخازن (
        رقم_المخزن COUNTER PRIMARY KEY,
        كود_المخزن TEXT(20),
        اسم_المخزن TEXT(100),
        نوع_المخزن TEXT(50),
        موقع_المخزن TEXT(200),
        مسؤول_المخزن TEXT(100),
        نشط YESNO
    )")
    
    # 3. مراكز التكلفة
    $access.DoCmd.RunSQL("CREATE TABLE مراكز_التكلفة (
        رقم_مركز_التكلفة COUNTER PRIMARY KEY,
        كود_مركز_التكلفة TEXT(20),
        اسم_مركز_التكلفة TEXT(100),
        نوع_مركز_التكلفة TEXT(50),
        معدل_التحميل_بالساعة CURRENCY,
        نشط YESNO
    )")
    
    # 4. الموردين
    $access.DoCmd.RunSQL("CREATE TABLE الموردين (
        رقم_المورد COUNTER PRIMARY KEY,
        كود_المورد TEXT(20),
        اسم_المورد TEXT(100),
        اسم_جهة_الاتصال TEXT(100),
        عنوان_المورد MEMO,
        هاتف_المورد TEXT(50),
        ايميل_المورد TEXT(100),
        شروط_الدفع TEXT(100),
        حد_الائتمان CURRENCY,
        تقييم_المورد INTEGER,
        نشط YESNO
    )")
    
    # 5. العملاء
    $access.DoCmd.RunSQL("CREATE TABLE العملاء (
        رقم_العميل COUNTER PRIMARY KEY,
        كود_العميل TEXT(20),
        اسم_العميل TEXT(100),
        نوع_العميل TEXT(20),
        هاتف_العميل TEXT(50),
        عنوان_العميل MEMO,
        حد_الائتمان CURRENCY,
        نسبة_الخصم DOUBLE,
        نشط YESNO
    )")
    
    # 6. المواد الخام
    $access.DoCmd.RunSQL("CREATE TABLE المواد_الخام (
        رقم_المادة COUNTER PRIMARY KEY,
        كود_المادة TEXT(50),
        اسم_المادة TEXT(100),
        مجموعة_المادة TEXT(50),
        رقم_وحدة_القياس LONG,
        رقم_المورد_الافتراضي LONG,
        الحد_الأدنى DOUBLE,
        نقطة_إعادة_الطلب DOUBLE,
        التكلفة_المعيارية CURRENCY,
        آخر_سعر_شراء CURRENCY,
        مدة_الصلاحية INTEGER,
        نشط YESNO
    )")
    
    # 7. المنتجات التامة
    $access.DoCmd.RunSQL("CREATE TABLE المنتجات_التامة (
        رقم_المنتج COUNTER PRIMARY KEY,
        كود_المنتج TEXT(50),
        اسم_المنتج TEXT(100),
        مجموعة_المنتج TEXT(50),
        رقم_وحدة_القياس LONG,
        الوزن_الصافي DOUBLE,
        سعر_البيع_التجزئة CURRENCY,
        سعر_البيع_الجملة CURRENCY,
        التكلفة_المعيارية CURRENCY,
        هامش_الربح_المستهدف DOUBLE,
        مدة_الصلاحية INTEGER,
        نشط YESNO
    )")
    
    # 8. الوصفات
    $access.DoCmd.RunSQL("CREATE TABLE الوصفات (
        رقم_الوصفة COUNTER PRIMARY KEY,
        كود_الوصفة TEXT(50),
        اسم_الوصفة TEXT(100),
        رقم_المنتج LONG,
        كمية_الإنتاج DOUBLE,
        رقم_وحدة_الإنتاج LONG,
        زمن_التحضير INTEGER,
        زمن_الطبخ INTEGER,
        درجة_حرارة_الطبخ DOUBLE,
        تكلفة_المواد_الخام CURRENCY,
        تكلفة_العمالة CURRENCY,
        التكلفة_الإجمالية CURRENCY,
        تكلفة_الوحدة CURRENCY,
        نشطة YESNO
    )")
    
    # 9. تفاصيل الوصفات
    $access.DoCmd.RunSQL("CREATE TABLE تفاصيل_الوصفات (
        رقم_التفصيل COUNTER PRIMARY KEY,
        رقم_الوصفة LONG,
        رقم_المادة LONG,
        الكمية_المطلوبة DOUBLE,
        رقم_وحدة_القياس LONG,
        نسبة_الفاقد DOUBLE,
        الكمية_الصافية DOUBLE,
        التكلفة_المعيارية CURRENCY,
        إجمالي_التكلفة CURRENCY,
        ترتيب_الإضافة INTEGER
    )")
    
    # 10. فواتير الشراء
    $access.DoCmd.RunSQL("CREATE TABLE فواتير_الشراء (
        رقم_الفاتورة COUNTER PRIMARY KEY,
        رقم_فاتورة_المورد TEXT(50),
        رقم_المورد LONG,
        تاريخ_الفاتورة DATETIME,
        تاريخ_الاستحقاق DATETIME,
        رقم_المخزن LONG,
        إجمالي_الفاتورة CURRENCY,
        نسبة_الخصم DOUBLE,
        قيمة_الخصم CURRENCY,
        نسبة_الضريبة DOUBLE,
        قيمة_الضريبة CURRENCY,
        الصافي_بعد_الخصم CURRENCY,
        إجمالي_شامل_الضريبة CURRENCY,
        حالة_الفاتورة TEXT(20),
        حالة_السداد TEXT(20),
        المبلغ_المسدد CURRENCY,
        المبلغ_المتبقي CURRENCY,
        مستخدم_الإدخال TEXT(50),
        تاريخ_الإدخال DATETIME
    )")
    
    # 11. تفاصيل فواتير الشراء
    $access.DoCmd.RunSQL("CREATE TABLE تفاصيل_فواتير_الشراء (
        رقم_التفصيل COUNTER PRIMARY KEY,
        رقم_الفاتورة LONG,
        رقم_المادة LONG,
        الكمية DOUBLE,
        سعر_الوحدة CURRENCY,
        إجمالي_السطر CURRENCY,
        نسبة_الخصم DOUBLE,
        قيمة_الخصم CURRENCY,
        الصافي_بعد_الخصم CURRENCY,
        تاريخ_الصلاحية DATETIME,
        رقم_اللوط TEXT(50),
        بلد_المنشأ TEXT(50)
    )")
    
    # 12. أوامر الإنتاج
    $access.DoCmd.RunSQL("CREATE TABLE أوامر_الإنتاج (
        رقم_أمر_الإنتاج COUNTER PRIMARY KEY,
        رقم_المنتج LONG,
        رقم_الوصفة LONG,
        الكمية_المطلوبة DOUBLE,
        الكمية_المنتجة_فعلياً DOUBLE,
        تاريخ_الأمر DATETIME,
        تاريخ_البدء_المخطط DATETIME,
        تاريخ_الانتهاء_المخطط DATETIME,
        تاريخ_البدء_الفعلي DATETIME,
        تاريخ_الانتهاء_الفعلي DATETIME,
        رقم_مركز_التكلفة LONG,
        رقم_المخزن_الإنتاج LONG,
        تكلفة_المواد_الخام CURRENCY,
        تكلفة_العمالة CURRENCY,
        التكاليف_المباشرة CURRENCY,
        التكاليف_غير_المباشرة CURRENCY,
        إجمالي_التكلفة CURRENCY,
        تكلفة_الوحدة CURRENCY,
        حالة_الأمر TEXT(20),
        نسبة_الإنجاز DOUBLE,
        مستخدم_الإدخال TEXT(50),
        تاريخ_الإدخال DATETIME
    )")
    
    # 13. حركات المخزون
    $access.DoCmd.RunSQL("CREATE TABLE حركات_المخزون (
        رقم_الحركة COUNTER PRIMARY KEY,
        رقم_المادة LONG,
        رقم_المخزن LONG,
        نوع_الحركة TEXT(20),
        مصدر_الحركة TEXT(30),
        رقم_المصدر LONG,
        الكمية DOUBLE,
        سعر_الوحدة CURRENCY,
        إجمالي_القيمة CURRENCY,
        الرصيد_قبل_الحركة DOUBLE,
        الرصيد_بعد_الحركة DOUBLE,
        المتوسط_المرجح_قبل CURRENCY,
        المتوسط_المرجح_بعد CURRENCY,
        تاريخ_الحركة DATETIME,
        رقم_اللوط TEXT(50),
        ملاحظات MEMO,
        مستخدم_الإدخال TEXT(50)
    )")
    
    # 14. أرصدة المخزون
    $access.DoCmd.RunSQL("CREATE TABLE أرصدة_المخزون (
        رقم_الرصيد COUNTER PRIMARY KEY,
        رقم_المادة LONG,
        رقم_المخزن LONG,
        الكمية_المتاحة DOUBLE,
        الكمية_المحجوزة DOUBLE,
        الكمية_الصافية DOUBLE,
        المتوسط_المرجح CURRENCY,
        إجمالي_القيمة CURRENCY,
        تاريخ_آخر_حركة DATETIME,
        تاريخ_آخر_جرد DATETIME
    )")
    
    Write-Host "✅ تم إنشاء 14 جدول أساسي" -ForegroundColor Green
    
    # ==================== إدخال البيانات الأساسية ====================
    Write-Host "📊 إدخال البيانات الأساسية..." -ForegroundColor Cyan
    
    # وحدات القياس
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, معامل_التحويل, وحدة_أساسية, نشطة) VALUES ('كيلو جرام', 'كجم', 'وزن', 1, True, True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, معامل_التحويل, وحدة_أساسية, نشطة) VALUES ('جرام', 'جم', 'وزن', 0.001, False, True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, معامل_التحويل, وحدة_أساسية, نشطة) VALUES ('لتر', 'لتر', 'حجم', 1, True, True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, معامل_التحويل, وحدة_أساسية, نشطة) VALUES ('قطعة', 'قطعة', 'عدد', 1, True, True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, معامل_التحويل, وحدة_أساسية, نشطة) VALUES ('عبوة', 'عبوة', 'تعبئة', 1, True, True)")
    
    # المخازن
    $access.DoCmd.RunSQL("INSERT INTO المخازن (كود_المخزن, اسم_المخزن, نوع_المخزن, موقع_المخزن, مسؤول_المخزن, نشط) VALUES ('WH-RM', 'مخزن المواد الخام', 'مواد خام', 'الدور الأرضي - قسم أ', 'أحمد محمد', True)")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (كود_المخزن, اسم_المخزن, نوع_المخزن, موقع_المخزن, مسؤول_المخزن, نشط) VALUES ('WH-FG', 'مخزن المنتجات التامة', 'منتجات تامة', 'الدور الأرضي - قسم ب', 'فاطمة علي', True)")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (كود_المخزن, اسم_المخزن, نوع_المخزن, موقع_المخزن, مسؤول_المخزن, نشط) VALUES ('WH-PR', 'مخزن الإنتاج', 'إنتاج', 'الدور الأول', 'محمد حسن', True)")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (كود_المخزن, اسم_المخزن, نوع_المخزن, موقع_المخزن, مسؤول_المخزن, نشط) VALUES ('WH-QC', 'مخزن الحجر الصحي', 'حجر صحي', 'الدور الأرضي - قسم ج', 'سارة أحمد', True)")
    
    Write-Host "✅ تم إدخال البيانات الأساسية" -ForegroundColor Green
    
    # حفظ وإغلاق
    $access.CloseCurrentDatabase()
    $access.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    
    Write-Host "🎉 تم إنشاء النظام الأساسي بنجاح!" -ForegroundColor Green
    Write-Host "📁 الملف: $dbPath" -ForegroundColor Yellow
    Write-Host "📊 الجداول: 14 جدول احترافي" -ForegroundColor White
    Write-Host "📈 البيانات: وحدات القياس ومخازن أساسية" -ForegroundColor White
    
} catch {
    Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
    if ($access) {
        try {
            $access.CloseCurrentDatabase()
            $access.Quit()
        } catch {}
    }
}

Write-Host "`n✅ النظام الأساسي جاهز!" -ForegroundColor Green
