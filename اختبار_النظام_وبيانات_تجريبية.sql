-- =====================================================
-- اختبار النظام وإدخال بيانات تجريبية
-- نظام إدارة تصنيع المعمول بالجوز
-- =====================================================

-- =====================================================
-- إدخال موردين إضافيين للاختبار
-- =====================================================
INSERT INTO الموردين (اسم_المورد, اسم_جهة_الاتصال, عنوان_المورد, هاتف_المورد, ايميل_المورد, شروط_الدفع, حد_الائتمان) VALUES
('شركة الدقيق المصري', 'أحمد محمد علي', 'شارع الجمهورية، القاهرة', '02-12345678', '<EMAIL>', 'آجل 30 يوم', 50000),
('مؤسسة السكر والحلويات', 'فاطمة حسن', 'شارع النيل، الجيزة', '02-87654321', '<EMAIL>', 'نقدي', 25000),
('شركة المكسرات الذهبية', 'محمد أحمد', 'شارع الهرم، الجيزة', '02-11223344', '<EMAIL>', 'آجل 15 يوم', 30000),
('مصنع الزيوت الطبيعية', 'علي حسين', 'المنطقة الصناعية، 6 أكتوبر', '02-55667788', '<EMAIL>', 'آجل 45 يوم', 40000),
('شركة منتجات الألبان', 'سارة محمود', 'شارع التحرير، القاهرة', '02-99887766', '<EMAIL>', 'نقدي', 20000);

-- =====================================================
-- إدخال فاتورة شراء تجريبية
-- =====================================================
INSERT INTO فواتير_الشراء (رقم_فاتورة_المورد, رقم_المورد, تاريخ_الفاتورة, تاريخ_الاستحقاق, رقم_المخزن, نسبة_الضريبة, حالة_الفاتورة, مستخدم_الإدخال) VALUES
('INV-2024-001', 1, '2024-01-15', '2024-02-14', 1, 14.0, 'معتمدة', 'admin'),
('INV-2024-002', 2, '2024-01-16', '2024-02-15', 1, 14.0, 'معتمدة', 'admin'),
('INV-2024-003', 3, '2024-01-17', '2024-02-16', 1, 14.0, 'معتمدة', 'admin');

-- =====================================================
-- إدخال تفاصيل فواتير الشراء
-- =====================================================
-- تفاصيل الفاتورة الأولى (دقيق وسكر)
INSERT INTO تفاصيل_فواتير_الشراء (رقم_الفاتورة, رقم_المادة, الكمية, سعر_الوحدة, إجمالي_السطر, الصافي_بعد_الخصم, تاريخ_الصلاحية, رقم_اللوط) VALUES
(1, 1, 100, 25.50, 2550.00, 2550.00, '2024-12-31', 'LOT-DQ-001'),
(1, 2, 50, 18.75, 937.50, 937.50, '2024-11-30', 'LOT-SG-001');

-- تفاصيل الفاتورة الثانية (زبدة وجوز)
INSERT INTO تفاصيل_فواتير_الشراء (رقم_الفاتورة, رقم_المادة, الكمية, سعر_الوحدة, إجمالي_السطر, الصافي_بعد_الخصم, تاريخ_الصلاحية, رقم_اللوط) VALUES
(2, 3, 25, 85.00, 2125.00, 2125.00, '2024-06-30', 'LOT-ZB-001'),
(2, 4, 30, 120.00, 3600.00, 3600.00, '2024-08-31', 'LOT-JZ-001');

-- تفاصيل الفاتورة الثالثة (بيض وفانيليا)
INSERT INTO تفاصيل_فواتير_الشراء (رقم_الفاتورة, رقم_المادة, الكمية, سعر_الوحدة, إجمالي_السطر, الصافي_بعد_الخصم, تاريخ_الصلاحية, رقم_اللوط) VALUES
(3, 5, 200, 2.50, 500.00, 500.00, '2024-02-15', 'LOT-BC-001'),
(3, 6, 5, 45.00, 225.00, 225.00, '2025-01-31', 'LOT-VN-001'),
(3, 7, 10, 12.00, 120.00, 120.00, '2025-12-31', 'LOT-BP-001'),
(3, 8, 20, 3.50, 70.00, 70.00, '2026-01-31', 'LOT-ML-001'),
(3, 9, 15, 35.00, 525.00, 525.00, '2024-10-31', 'LOT-HL-001'),
(3, 10, 25, 28.00, 700.00, 700.00, '2024-09-30', 'LOT-SM-001');

-- =====================================================
-- تحديث إجماليات الفواتير
-- =====================================================
UPDATE فواتير_الشراء SET 
    إجمالي_الفاتورة = (SELECT SUM(الصافي_بعد_الخصم) FROM تفاصيل_فواتير_الشراء WHERE رقم_الفاتورة = فواتير_الشراء.رقم_الفاتورة),
    قيمة_الضريبة = (SELECT SUM(الصافي_بعد_الخصم) FROM تفاصيل_فواتير_الشراء WHERE رقم_الفاتورة = فواتير_الشراء.رقم_الفاتورة) * نسبة_الضريبة / 100,
    إجمالي_شامل_الضريبة = (SELECT SUM(الصافي_بعد_الخصم) FROM تفاصيل_فواتير_الشراء WHERE رقم_الفاتورة = فواتير_الشراء.رقم_الفاتورة) * (1 + نسبة_الضريبة / 100),
    الصافي_بعد_الخصم = (SELECT SUM(الصافي_بعد_الخصم) FROM تفاصيل_فواتير_الشراء WHERE رقم_الفاتورة = فواتير_الشراء.رقم_الفاتورة) * (1 + نسبة_الضريبة / 100);

-- =====================================================
-- إدخال حركات المخزون لفواتير الشراء
-- =====================================================
-- حركات الفاتورة الأولى
INSERT INTO حركات_المخزون (رقم_المادة, رقم_المخزن, نوع_الحركة, مصدر_الحركة, رقم_المصدر, الكمية, سعر_الوحدة, إجمالي_القيمة, الرصيد_قبل_الحركة, الرصيد_بعد_الحركة, المتوسط_المرجح_قبل, المتوسط_المرجح_بعد, تاريخ_الحركة, رقم_اللوط, ملاحظات, مستخدم_الإدخال) VALUES
(1, 1, 'دخول', 'فاتورة_شراء', 1, 100, 25.50, 2550.00, 0, 100, 0, 25.50, '2024-01-15', 'LOT-DQ-001', 'دخول من فاتورة شراء رقم 1', 'admin'),
(2, 1, 'دخول', 'فاتورة_شراء', 1, 50, 18.75, 937.50, 0, 50, 0, 18.75, '2024-01-15', 'LOT-SG-001', 'دخول من فاتورة شراء رقم 1', 'admin');

-- حركات الفاتورة الثانية
INSERT INTO حركات_المخزون (رقم_المادة, رقم_المخزن, نوع_الحركة, مصدر_الحركة, رقم_المصدر, الكمية, سعر_الوحدة, إجمالي_القيمة, الرصيد_قبل_الحركة, الرصيد_بعد_الحركة, المتوسط_المرجح_قبل, المتوسط_المرجح_بعد, تاريخ_الحركة, رقم_اللوط, ملاحظات, مستخدم_الإدخال) VALUES
(3, 1, 'دخول', 'فاتورة_شراء', 2, 25, 85.00, 2125.00, 0, 25, 0, 85.00, '2024-01-16', 'LOT-ZB-001', 'دخول من فاتورة شراء رقم 2', 'admin'),
(4, 1, 'دخول', 'فاتورة_شراء', 2, 30, 120.00, 3600.00, 0, 30, 0, 120.00, '2024-01-16', 'LOT-JZ-001', 'دخول من فاتورة شراء رقم 2', 'admin');

-- حركات الفاتورة الثالثة
INSERT INTO حركات_المخزون (رقم_المادة, رقم_المخزن, نوع_الحركة, مصدر_الحركة, رقم_المصدر, الكمية, سعر_الوحدة, إجمالي_القيمة, الرصيد_قبل_الحركة, الرصيد_بعد_الحركة, المتوسط_المرجح_قبل, المتوسط_المرجح_بعد, تاريخ_الحركة, رقم_اللوط, ملاحظات, مستخدم_الإدخال) VALUES
(5, 1, 'دخول', 'فاتورة_شراء', 3, 200, 2.50, 500.00, 0, 200, 0, 2.50, '2024-01-17', 'LOT-BC-001', 'دخول من فاتورة شراء رقم 3', 'admin'),
(6, 1, 'دخول', 'فاتورة_شراء', 3, 5, 45.00, 225.00, 0, 5, 0, 45.00, '2024-01-17', 'LOT-VN-001', 'دخول من فاتورة شراء رقم 3', 'admin'),
(7, 1, 'دخول', 'فاتورة_شراء', 3, 10, 12.00, 120.00, 0, 10, 0, 12.00, '2024-01-17', 'LOT-BP-001', 'دخول من فاتورة شراء رقم 3', 'admin'),
(8, 1, 'دخول', 'فاتورة_شراء', 3, 20, 3.50, 70.00, 0, 20, 0, 3.50, '2024-01-17', 'LOT-ML-001', 'دخول من فاتورة شراء رقم 3', 'admin'),
(9, 1, 'دخول', 'فاتورة_شراء', 3, 15, 35.00, 525.00, 0, 15, 0, 35.00, '2024-01-17', 'LOT-HL-001', 'دخول من فاتورة شراء رقم 3', 'admin'),
(10, 1, 'دخول', 'فاتورة_شراء', 3, 25, 28.00, 700.00, 0, 25, 0, 28.00, '2024-01-17', 'LOT-SM-001', 'دخول من فاتورة شراء رقم 3', 'admin');

-- =====================================================
-- إدخال أرصدة المخزون
-- =====================================================
INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المخزن, الكمية_المتاحة, الكمية_المحجوزة, الكمية_الصافية, المتوسط_المرجح, إجمالي_القيمة, تاريخ_آخر_حركة) VALUES
(1, 1, 100, 0, 100, 25.50, 2550.00, '2024-01-15'),
(2, 1, 50, 0, 50, 18.75, 937.50, '2024-01-15'),
(3, 1, 25, 0, 25, 85.00, 2125.00, '2024-01-16'),
(4, 1, 30, 0, 30, 120.00, 3600.00, '2024-01-16'),
(5, 1, 200, 0, 200, 2.50, 500.00, '2024-01-17'),
(6, 1, 5, 0, 5, 45.00, 225.00, '2024-01-17'),
(7, 1, 10, 0, 10, 12.00, 120.00, '2024-01-17'),
(8, 1, 20, 0, 20, 3.50, 70.00, '2024-01-17'),
(9, 1, 15, 0, 15, 35.00, 525.00, '2024-01-17'),
(10, 1, 25, 0, 25, 28.00, 700.00, '2024-01-17');

-- =====================================================
-- إدخال أمر إنتاج تجريبي
-- =====================================================
INSERT INTO أوامر_الإنتاج (رقم_المنتج, رقم_الوصفة, الكمية_المطلوبة, تاريخ_الأمر, تاريخ_البدء_المخطط, تاريخ_الانتهاء_المخطط, رقم_مركز_التكلفة, رقم_المخزن_الإنتاج, حالة_الأمر, مستخدم_الإدخال) VALUES
(1, 1, 500, '2024-01-20', '2024-01-22', '2024-01-24', 1, 1, 'مخطط', 'admin');

-- =====================================================
-- إدخال تكاليف الأيدي العاملة التجريبية
-- =====================================================
INSERT INTO تكاليف_الأيدي_العاملة (رقم_أمر_الإنتاج, اسم_العامل, نوع_العمل, عدد_الساعات, معدل_الساعة, إجمالي_التكلفة, تاريخ_العمل) VALUES
(1, 'أحمد محمد', 'تحضير وخلط', 4.0, 25.00, 100.00, '2024-01-22'),
(1, 'فاطمة علي', 'تشكيل', 6.0, 20.00, 120.00, '2024-01-22'),
(1, 'محمد حسن', 'خبز', 3.0, 30.00, 90.00, '2024-01-23'),
(1, 'سارة أحمد', 'تعبئة وتغليف', 2.0, 18.00, 36.00, '2024-01-24');

-- =====================================================
-- إدخال التكاليف المباشرة التجريبية
-- =====================================================
INSERT INTO التكاليف_المباشرة (رقم_أمر_الإنتاج, نوع_التكلفة, وصف_التكلفة, قيمة_التكلفة, تاريخ_التكلفة) VALUES
(1, 'كهرباء', 'استهلاك الكهرباء للأفران والمعدات', 150.00, '2024-01-22'),
(1, 'غاز', 'استهلاك الغاز للأفران', 80.00, '2024-01-22'),
(1, 'مياه', 'استهلاك المياه للتنظيف', 25.00, '2024-01-22'),
(1, 'صيانة', 'صيانة دورية للمعدات', 45.00, '2024-01-23');

-- =====================================================
-- إدخال التكاليف غير المباشرة التجريبية
-- =====================================================
INSERT INTO التكاليف_غير_المباشرة (رقم_مركز_التكلفة, نوع_التكلفة, وصف_التكلفة, قيمة_التكلفة, الفترة_من, الفترة_إلى, طريقة_التوزيع) VALUES
(1, 'إيجار', 'إيجار المصنع الشهري', 5000.00, '2024-01-01', '2024-01-31', 'حسب_الساعات'),
(1, 'إدارة', 'رواتب الإدارة', 8000.00, '2024-01-01', '2024-01-31', 'حسب_التكلفة'),
(1, 'تأمين', 'تأمين المصنع والمعدات', 1200.00, '2024-01-01', '2024-01-31', 'حسب_التكلفة'),
(1, 'استهلاك', 'استهلاك المعدات والآلات', 2500.00, '2024-01-01', '2024-01-31', 'حسب_الساعات');

-- =====================================================
-- استعلامات اختبار النظام
-- =====================================================

-- اختبار تقرير المخزون الحالي
SELECT 'اختبار تقرير المخزون الحالي' AS اختبار;
SELECT * FROM تقرير_المخزون_الحالي;

-- اختبار تقرير حركات المخزون
SELECT 'اختبار تقرير حركات المخزون' AS اختبار;
SELECT * FROM تقرير_حركات_المخزون WHERE تاريخ_الحركة >= '2024-01-15';

-- اختبار تحليل تكاليف الوصفات
SELECT 'اختبار تحليل تكاليف الوصفات' AS اختبار;
SELECT * FROM تحليل_تكاليف_الوصفات;

-- اختبار تقرير المشتريات
SELECT 'اختبار تقرير المشتريات' AS اختبار;
SELECT * FROM تقرير_المشتريات WHERE تاريخ_الفاتورة >= '2024-01-15';

-- اختبار المواد تحت الحد الأدنى
SELECT 'اختبار المواد تحت الحد الأدنى' AS اختبار;
SELECT * FROM المواد_تحت_الحد_الأدنى;

-- =====================================================
-- تحقق من سلامة البيانات
-- =====================================================

-- التحقق من صحة أرصدة المخزون
SELECT 'التحقق من صحة أرصدة المخزون' AS فحص;
SELECT 
    م.اسم_المادة,
    ر.الكمية_المتاحة AS الرصيد_في_الجدول,
    COALESCE(SUM(CASE WHEN ح.نوع_الحركة = 'دخول' THEN ح.الكمية ELSE -ح.الكمية END), 0) AS الرصيد_من_الحركات,
    ر.الكمية_المتاحة - COALESCE(SUM(CASE WHEN ح.نوع_الحركة = 'دخول' THEN ح.الكمية ELSE -ح.الكمية END), 0) AS الفرق
FROM أرصدة_المخزون ر
INNER JOIN المواد_الخام م ON ر.رقم_المادة = م.رقم_المادة
LEFT JOIN حركات_المخزون ح ON ر.رقم_المادة = ح.رقم_المادة AND ر.رقم_المخزن = ح.رقم_المخزن
GROUP BY م.اسم_المادة, ر.الكمية_المتاحة
HAVING ABS(ر.الكمية_المتاحة - COALESCE(SUM(CASE WHEN ح.نوع_الحركة = 'دخول' THEN ح.الكمية ELSE -ح.الكمية END), 0)) > 0.01;

-- التحقق من صحة قيم المخزون
SELECT 'التحقق من صحة قيم المخزون' AS فحص;
SELECT 
    م.اسم_المادة,
    ر.إجمالي_القيمة AS القيمة_في_الجدول,
    ر.الكمية_المتاحة * ر.المتوسط_المرجح AS القيمة_المحسوبة,
    ABS(ر.إجمالي_القيمة - (ر.الكمية_المتاحة * ر.المتوسط_المرجح)) AS الفرق
FROM أرصدة_المخزون ر
INNER JOIN المواد_الخام م ON ر.رقم_المادة = م.رقم_المادة
WHERE ABS(ر.إجمالي_القيمة - (ر.الكمية_المتاحة * ر.المتوسط_المرجح)) > 0.01;

-- =====================================================
-- إحصائيات النظام
-- =====================================================
SELECT 'إحصائيات النظام' AS تقرير;

SELECT 'عدد المواد الخام' AS البيان, COUNT(*) AS القيمة FROM المواد_الخام WHERE نشط = True
UNION ALL
SELECT 'عدد المنتجات التامة', COUNT(*) FROM المنتجات_التامة WHERE نشط = True
UNION ALL
SELECT 'عدد الموردين', COUNT(*) FROM الموردين WHERE نشط = True
UNION ALL
SELECT 'عدد المخازن', COUNT(*) FROM المخازن WHERE نشط = True
UNION ALL
SELECT 'عدد الوصفات النشطة', COUNT(*) FROM الوصفات WHERE نشطة = True
UNION ALL
SELECT 'عدد فواتير الشراء', COUNT(*) FROM فواتير_الشراء
UNION ALL
SELECT 'عدد أوامر الإنتاج', COUNT(*) FROM أوامر_الإنتاج
UNION ALL
SELECT 'إجمالي قيمة المخزون', ROUND(SUM(إجمالي_القيمة), 2) FROM أرصدة_المخزون
UNION ALL
SELECT 'عدد حركات المخزون', COUNT(*) FROM حركات_المخزون;
