# نظام إدارة تصنيع المعمول بالجوز 🥜

نظام إدارة تصنيع احترافي مصمم خصيصاً لإنتاج المعمول بالجوز باستخدام Microsoft Access مع دعم كامل للغة العربية.

## 🌟 المميزات الرئيسية

### 📦 إدارة المخزون المتقدمة
- **نظام المتوسط المرجح**: تحديث تلقائي للتكاليف بعد كل عملية
- **مخازن متعددة**: دعم إدارة عدة مخازن مع وحدات قياس مختلفة
- **تتبع الحركات**: سجل شامل لجميع حركات الدخول والخروج
- **تنبيهات ذكية**: تنبيهات للمواد تحت الحد الأدنى
- **إدارة الصلاحية**: تتبع تواريخ الصلاحية وأرقام اللوط

### 🏭 إدارة الإنتاج
- **الوصفات الذكية**: إنشاء وصفات تفصيلية للمنتجات
- **أوامر الإنتاج**: إدارة شاملة لدورة الإنتاج
- **صرف تلقائي**: صرف المواد الخام تلقائياً حسب الوصفة
- **تتبع التقدم**: متابعة حالة أوامر الإنتاج
- **حساب الفاقد**: احتساب نسب الفاقد المتوقع

### 💰 محاسبة التكاليف
- **تكاليف شاملة**: تكاليف المواد الخام، الأيدي العاملة، والتكاليف المباشرة وغير المباشرة
- **مراكز التكلفة**: توزيع التكاليف على مراكز مختلفة
- **تحليل الربحية**: حساب تكلفة الوحدة وهامش الربح
- **مقارنة التكاليف**: مقارنة التكاليف المخططة مع الفعلية

### 🇸🇦 واجهة عربية كاملة
- **اللهجة المصرية**: جميع النصوص باللهجة المصرية المفهومة
- **تخطيط عربي**: تصميم يدعم الكتابة من اليمين لليسار
- **خطوط واضحة**: استخدام خطوط عربية واضحة ومقروءة

## 📋 المتطلبات التقنية

- **نظام التشغيل**: Windows 10 أو أحدث
- **البرنامج**: Microsoft Access 2016 أو أحدث
- **الذاكرة**: 4 جيجابايت RAM كحد أدنى
- **مساحة القرص**: 500 ميجابايت مساحة فارغة
- **الشاشة**: دقة 1024x768 كحد أدنى

## 🚀 التثبيت السريع

### الطريقة الأولى: التثبيت التلقائي
```powershell
# تشغيل سكريبت PowerShell كمدير
.\إنشاء_قاعدة_البيانات.ps1
```

### الطريقة الثانية: التثبيت اليدوي
1. افتح Microsoft Access
2. أنشئ قاعدة بيانات جديدة
3. استورد الجداول من ملف `نظام_إدارة_تصنيع_المعمول.sql`
4. أنشئ النماذج حسب التصميم المرفق

## 📁 هيكل الملفات

```
📦 نظام إدارة تصنيع المعمول
├── 📄 نظام_إدارة_تصنيع_المعمول.sql      # هيكل قاعدة البيانات
├── 📄 وحدات_VBA_نظام_المخزون.txt         # كود VBA للمخزون
├── 📄 الاستعلامات_المحاسبية.sql          # الاستعلامات والتقارير
├── 📄 تصميم_النماذج_والعلاقات.txt         # تصميم النماذج
├── 📄 إنشاء_قاعدة_البيانات.ps1           # سكريبت التثبيت
├── 📄 دليل_المستخدم.md                   # دليل المستخدم الشامل
└── 📄 README.md                          # هذا الملف
```

## 🗃️ هيكل قاعدة البيانات

### الجداول الأساسية
- **وحدات_القياس**: إدارة وحدات القياس المختلفة
- **المخازن**: بيانات المخازن ومسؤوليها
- **الموردين**: معلومات الموردين وشروط التعامل
- **العملاء**: بيانات العملاء (اختياري)
- **المواد_الخام**: كتالوج المواد الخام
- **المنتجات_التامة**: كتالوج المنتجات النهائية
- **مراكز_التكلفة**: أقسام الإنتاج والتكلفة

### جداول المعاملات
- **فواتير_الشراء**: فواتير شراء المواد الخام
- **تفاصيل_فواتير_الشراء**: تفاصيل كل فاتورة
- **أوامر_الإنتاج**: أوامر تصنيع المنتجات
- **حركات_المخزون**: سجل جميع حركات المخزون
- **أرصدة_المخزون**: الأرصدة الحالية والمتوسط المرجح

### جداول الوصفات والتكاليف
- **الوصفات**: وصفات المنتجات
- **تفاصيل_الوصفات**: مكونات كل وصفة
- **تكاليف_الأيدي_العاملة**: تكاليف العمالة
- **التكاليف_المباشرة**: التكاليف المباشرة
- **التكاليف_غير_المباشرة**: التكاليف العامة

## 🔧 الوظائف الرئيسية

### إدارة المشتريات
```sql
-- إضافة فاتورة شراء جديدة
CALL معالجة_فاتورة_شراء(رقم_الفاتورة)
```

### إدارة الإنتاج
```sql
-- صرف مواد خام لأمر إنتاج
CALL صرف_مواد_خام_لأمر_إنتاج(رقم_أمر_الإنتاج)
```

### حساب التكاليف
```sql
-- حساب المتوسط المرجح الجديد
SELECT حساب_المتوسط_المرجح_الجديد(رقم_المادة, رقم_المخزن, كمية_جديدة, سعر_جديد)
```

## 📊 التقارير المتاحة

### تقارير المخزون
- **تقرير المخزون الحالي**: أرصدة جميع المواد
- **تقرير حركات المخزون**: سجل الحركات التفصيلي
- **المواد تحت الحد الأدنى**: تنبيهات إعادة الطلب

### تقارير الإنتاج
- **تقرير الإنتاج**: حالة أوامر الإنتاج
- **تحليل تكاليف الوصفات**: تكلفة كل وصفة
- **مقارنة التكاليف**: مخطط مقابل فعلي

### تقارير المشتريات
- **تقرير المشتريات**: ملخص فواتير الشراء
- **تفاصيل المشتريات**: تفاصيل كل فاتورة

## 🛡️ الأمان وسلامة البيانات

### حماية البيانات
- **قيود المرجعية**: منع حذف البيانات المرتبطة
- **التحقق من الصحة**: فحص صحة البيانات قبل الحفظ
- **منع التضارب**: حماية من العمليات المتضاربة
- **تسجيل العمليات**: سجل شامل لجميع التغييرات

### النسخ الاحتياطي
- **نسخ تلقائية**: جدولة النسخ الاحتياطية
- **ضغط البيانات**: تحسين حجم قاعدة البيانات
- **استعادة سريعة**: استعادة البيانات بسهولة

## 🎯 حالات الاستخدام

### مصنع صغير
- إدارة 50-100 مادة خام
- 5-10 منتجات نهائية
- 2-3 مخازن
- 10-20 أمر إنتاج شهرياً

### مصنع متوسط
- إدارة 200-500 مادة خام
- 20-50 منتج نهائي
- 5-10 مخازن
- 50-100 أمر إنتاج شهرياً

### مصنع كبير
- إدارة 1000+ مادة خام
- 100+ منتج نهائي
- 10+ مخزن
- 200+ أمر إنتاج شهرياً

## 📈 مثال عملي: إنتاج المعمول

### 1. إعداد الوصفة
```
معمول جوز صغير (100 قطعة):
- 2 كجم دقيق أبيض
- 800 جم سكر ناعم
- 600 جم زبدة
- 1.2 كجم جوز مقشر
- 6 بيضات
- 20 مل فانيليا
- 15 جم بيكنج باودر
- 5 جم ملح
- 100 جم حليب بودرة
- 300 جم سمن
```

### 2. إنشاء أمر إنتاج
- الكمية المطلوبة: 500 قطعة
- يحسب النظام تلقائياً: 5 × كميات الوصفة
- يصرف المواد من المخزون تلقائياً
- يحدث المتوسط المرجح للمواد

### 3. حساب التكاليف
- تكلفة المواد الخام: حسب المتوسط المرجح
- تكلفة الأيدي العاملة: حسب الساعات الفعلية
- التكاليف المباشرة: كهرباء، غاز، مياه
- التكاليف غير المباشرة: 15% من تكلفة المواد

## 🤝 المساهمة

نرحب بمساهماتكم لتطوير النظام:

1. **تقارير الأخطاء**: أبلغوا عن أي مشاكل تواجهونها
2. **اقتراحات التحسين**: شاركوا أفكاركم لتطوير النظام
3. **ترجمات**: مساعدة في ترجمة النظام للهجات أخرى
4. **توثيق**: تحسين الدليل والتوثيق

## 📞 الدعم الفني

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +20-xxx-xxx-xxxx
- **ساعات العمل**: الأحد - الخميس، 9 صباحاً - 5 مساءً

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- فريق تطوير Microsoft Access
- مجتمع المطورين العرب
- عملاؤنا الكرام لملاحظاتهم القيمة

---

**تم تطوير هذا النظام بعناية فائقة ليلبي احتياجات صناعة المعمول في الوطن العربي** 🇸🇦🇪🇬🇯🇴🇱🇧
