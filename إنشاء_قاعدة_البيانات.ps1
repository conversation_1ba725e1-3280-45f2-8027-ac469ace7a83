# =====================================================
# سكريبت PowerShell لإنشاء قاعدة بيانات Access
# نظام إدارة تصنيع المعمول بالجوز
# =====================================================

# التحقق من وجود Microsoft Access
try {
    $access = New-Object -ComObject Access.Application
    Write-Host "تم العثور على Microsoft Access" -ForegroundColor Green
} catch {
    Write-Host "خطأ: Microsoft Access غير مثبت على هذا الجهاز" -ForegroundColor Red
    exit 1
}

# إعداد المتغيرات
$dbPath = Join-Path $PSScriptRoot "نظام_إدارة_تصنيع_المعمول.accdb"
$sqlPath = Join-Path $PSScriptRoot "نظام_إدارة_تصنيع_المعمول.sql"

# حذف قاعدة البيانات إذا كانت موجودة
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force
    Write-Host "تم حذف قاعدة البيانات الموجودة" -ForegroundColor Yellow
}

try {
    # إنشاء قاعدة بيانات جديدة
    Write-Host "جاري إنشاء قاعدة البيانات..." -ForegroundColor Blue
    $access.NewCurrentDatabase($dbPath)
    
    # قراءة ملف SQL
    if (Test-Path $sqlPath) {
        $sqlContent = Get-Content $sqlPath -Raw -Encoding UTF8
        
        # تقسيم الاستعلامات
        $queries = $sqlContent -split ";\s*(?=CREATE|INSERT|ALTER|DROP)" | Where-Object { $_.Trim() -ne "" }
        
        Write-Host "جاري تنفيذ الاستعلامات..." -ForegroundColor Blue
        
        foreach ($query in $queries) {
            $query = $query.Trim()
            if ($query -ne "" -and -not $query.StartsWith("--")) {
                try {
                    # تنفيذ الاستعلام
                    $access.CurrentDb().Execute($query + ";")
                    Write-Host "تم تنفيذ: $($query.Substring(0, [Math]::Min(50, $query.Length)))..." -ForegroundColor Green
                } catch {
                    Write-Host "خطأ في تنفيذ: $($query.Substring(0, [Math]::Min(50, $query.Length)))..." -ForegroundColor Red
                    Write-Host "تفاصيل الخطأ: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    } else {
        Write-Host "تحذير: لم يتم العثور على ملف SQL" -ForegroundColor Yellow
    }
    
    # إنشاء النماذج الأساسية
    Write-Host "جاري إنشاء النماذج الأساسية..." -ForegroundColor Blue
    
    # نموذج الشاشة الرئيسية
    $mainForm = $access.CreateForm()
    $mainForm.Caption = "نظام إدارة تصنيع المعمول بالجوز"
    $mainForm.NavigationButtons = $false
    $mainForm.RecordSelectors = $false
    $mainForm.DividingLines = $false
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "فرم_الشاشة_الرئيسية")
    $access.DoCmd.Close(2, "فرم_الشاشة_الرئيسية")
    
    # نموذج المواد الخام
    $materialsForm = $access.CreateForm("المواد_الخام")
    $materialsForm.Caption = "إدارة المواد الخام"
    $access.DoCmd.Save(2, "فرم_المواد_الخام")
    $access.DoCmd.Close(2, "فرم_المواد_الخام")
    
    # نموذج المنتجات التامة
    $productsForm = $access.CreateForm("المنتجات_التامة")
    $productsForm.Caption = "إدارة المنتجات التامة"
    $access.DoCmd.Save(2, "فرم_المنتجات_التامة")
    $access.DoCmd.Close(2, "فرم_المنتجات_التامة")
    
    # نموذج الموردين
    $suppliersForm = $access.CreateForm("الموردين")
    $suppliersForm.Caption = "إدارة الموردين"
    $access.DoCmd.Save(2, "فرم_الموردين")
    $access.DoCmd.Close(2, "فرم_الموردين")
    
    # نموذج المخازن
    $warehousesForm = $access.CreateForm("المخازن")
    $warehousesForm.Caption = "إدارة المخازن"
    $access.DoCmd.Save(2, "فرم_المخازن")
    $access.DoCmd.Close(2, "فرم_المخازن")
    
    # نموذج فواتير الشراء
    $purchaseForm = $access.CreateForm("فواتير_الشراء")
    $purchaseForm.Caption = "فواتير الشراء"
    $access.DoCmd.Save(2, "فرم_فواتير_الشراء")
    $access.DoCmd.Close(2, "فرم_فواتير_الشراء")
    
    # نموذج أوامر الإنتاج
    $productionForm = $access.CreateForm("أوامر_الإنتاج")
    $productionForm.Caption = "أوامر الإنتاج"
    $access.DoCmd.Save(2, "فرم_أوامر_الإنتاج")
    $access.DoCmd.Close(2, "فرم_أوامر_الإنتاج")
    
    # نموذج الوصفات
    $recipesForm = $access.CreateForm("الوصفات")
    $recipesForm.Caption = "إدارة الوصفات"
    $access.DoCmd.Save(2, "فرم_الوصفات")
    $access.DoCmd.Close(2, "فرم_الوصفات")
    
    # إنشاء التقارير الأساسية
    Write-Host "جاري إنشاء التقارير..." -ForegroundColor Blue
    
    # تقرير المخزون
    $inventoryReport = $access.CreateReport("تقرير_المخزون_الحالي")
    $inventoryReport.Caption = "تقرير المخزون الحالي"
    $access.DoCmd.Save(3, "تقرير_المخزون")
    $access.DoCmd.Close(3, "تقرير_المخزون")
    
    # تقرير حركات المخزون
    $movementsReport = $access.CreateReport("تقرير_حركات_المخزون")
    $movementsReport.Caption = "تقرير حركات المخزون"
    $access.DoCmd.Save(3, "تقرير_حركات_المخزون")
    $access.DoCmd.Close(3, "تقرير_حركات_المخزون")
    
    # تقرير التكاليف
    $costsReport = $access.CreateReport("استعلام_إجمالي_تكاليف_الإنتاج")
    $costsReport.Caption = "تقرير التكاليف"
    $access.DoCmd.Save(3, "تقرير_التكاليف")
    $access.DoCmd.Close(3, "تقرير_التكاليف")
    
    # تقرير الإنتاج
    $productionReport = $access.CreateReport("تقرير_الإنتاج")
    $productionReport.Caption = "تقرير الإنتاج"
    $access.DoCmd.Save(3, "تقرير_الإنتاج_التفصيلي")
    $access.DoCmd.Close(3, "تقرير_الإنتاج_التفصيلي")
    
    # إنشاء وحدة VBA
    Write-Host "جاري إضافة وحدات VBA..." -ForegroundColor Blue
    
    $vbaPath = Join-Path $PSScriptRoot "وحدات_VBA_نظام_المخزون.txt"
    if (Test-Path $vbaPath) {
        $vbaContent = Get-Content $vbaPath -Raw -Encoding UTF8
        
        # إنشاء وحدة جديدة
        $module = $access.Modules.Add()
        $module.InsertText($vbaContent)
        $access.DoCmd.Save(5, "وحدة_إدارة_المخزون")
        $access.DoCmd.Close(5, "وحدة_إدارة_المخزون")
    }
    
    # ضغط وإصلاح قاعدة البيانات
    Write-Host "جاري ضغط وإصلاح قاعدة البيانات..." -ForegroundColor Blue
    $access.CompactRepair($dbPath, $dbPath)
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح!" -ForegroundColor Green
    Write-Host "مسار قاعدة البيانات: $dbPath" -ForegroundColor Green
    
    # فتح قاعدة البيانات
    $response = Read-Host "هل تريد فتح قاعدة البيانات الآن؟ (y/n)"
    if ($response -eq "y" -or $response -eq "Y") {
        Start-Process $dbPath
    }
    
} catch {
    Write-Host "خطأ في إنشاء قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # إغلاق Access
    if ($access) {
        $access.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    }
}

Write-Host "انتهى السكريبت" -ForegroundColor Blue
