=====================================================
تصميم النماذج والعلاقات لنظام إدارة تصنيع المعمول
=====================================================

=====================================================
العلاقات بين الجداول (Relationships)
=====================================================

1. العلاقات الأساسية:
   - وحدات_القياس (1) ←→ (∞) المواد_الخام
   - وحدات_القياس (1) ←→ (∞) المنتجات_التامة
   - الموردين (1) ←→ (∞) المواد_الخام (المورد الافتراضي)
   - الموردين (1) ←→ (∞) فواتير_الشراء
   - العملاء (1) ←→ (∞) فواتير_المبيعات
   - المخازن (1) ←→ (∞) فواتير_الشراء
   - المخازن (1) ←→ (∞) أوامر_الإنتاج
   - المخازن (1) ←→ (∞) حركات_المخزون
   - المخازن (1) ←→ (∞) أرصدة_المخزون

2. علاقات المعاملات:
   - فواتير_الشراء (1) ←→ (∞) تفاصيل_فواتير_الشراء
   - المواد_الخام (1) ←→ (∞) تفاصيل_فواتير_الشراء
   - المواد_الخام (1) ←→ (∞) حركات_المخزون
   - المواد_الخام (1) ←→ (∞) أرصدة_المخزون

3. علاقات الإنتاج:
   - المنتجات_التامة (1) ←→ (∞) الوصفات
   - المنتجات_التامة (1) ←→ (∞) أوامر_الإنتاج
   - الوصفات (1) ←→ (∞) تفاصيل_الوصفات
   - الوصفات (1) ←→ (∞) أوامر_الإنتاج
   - المواد_الخام (1) ←→ (∞) تفاصيل_الوصفات

4. علاقات التكاليف:
   - مراكز_التكلفة (1) ←→ (∞) أوامر_الإنتاج
   - مراكز_التكلفة (1) ←→ (∞) التكاليف_غير_المباشرة
   - أوامر_الإنتاج (1) ←→ (∞) تكاليف_الأيدي_العاملة
   - أوامر_الإنتاج (1) ←→ (∞) التكاليف_المباشرة
   - أوامر_الإنتاج (1) ←→ (∞) توزيع_التكاليف_غير_المباشرة

=====================================================
تصميم النماذج الأساسية
=====================================================

1. نموذج الشاشة الرئيسية (فرم_الشاشة_الرئيسية):
   - أزرار التنقل الرئيسية
   - إحصائيات سريعة (عدد المواد، المخزون، أوامر الإنتاج)
   - تنبيهات (مواد تحت الحد الأدنى، أوامر إنتاج متأخرة)
   - معلومات المستخدم والتاريخ

2. نموذج إدارة المواد الخام (فرم_المواد_الخام):
   - حقول إدخال بيانات المادة
   - قائمة منسدلة لوحدة القياس
   - قائمة منسدلة للمورد الافتراضي
   - قائمة منسدلة لمجموعة المادة
   - أزرار (جديد، حفظ، حذف، بحث)
   - جدول فرعي لعرض أرصدة المادة في المخازن المختلفة

3. نموذج إدارة المنتجات التامة (فرم_المنتجات_التامة):
   - حقول إدخال بيانات المنتج
   - قائمة منسدلة لوحدة القياس
   - قائمة منسدلة لمجموعة المنتج
   - أزرار (جديد، حفظ، حذف، بحث)
   - جدول فرعي لعرض الوصفات المرتبطة

4. نموذج إدارة الموردين (فرم_الموردين):
   - حقول إدخال بيانات المورد
   - تبويبات (البيانات الأساسية، بيانات الاتصال، الشروط المالية)
   - أزرار (جديد، حفظ، حذف، بحث)
   - جدول فرعي لعرض فواتير الشراء

5. نموذج إدارة المخازن (فرم_المخازن):
   - حقول إدخال بيانات المخزن
   - أزرار (جديد، حفظ، حذف، بحث)
   - جدول فرعي لعرض أرصدة المواد في المخزن

=====================================================
تصميم نماذج المعاملات
=====================================================

6. نموذج فاتورة الشراء (فرم_فاتورة_الشراء):
   - رأس الفاتورة (رقم المورد، تاريخ الفاتورة، المخزن)
   - جدول فرعي لتفاصيل الفاتورة
   - حسابات تلقائية (الإجمالي، الضريبة، الخصم)
   - أزرار (جديد، حفظ، اعتماد، طباعة، معالجة المخزون)
   - حالة الفاتورة (مسودة، معتمدة، ملغاة)

7. نموذج أمر الإنتاج (فرم_أمر_الإنتاج):
   - رأس الأمر (المنتج، الوصفة، الكمية، التواريخ)
   - جدول فرعي لعرض مكونات الوصفة
   - جدول فرعي لتكاليف الأيدي العاملة
   - جدول فرعي للتكاليف المباشرة
   - حسابات التكلفة التلقائية
   - أزرار (جديد، حفظ، بدء التنفيذ، إنهاء، صرف المواد)

8. نموذج الوصفات (فرم_الوصفات):
   - رأس الوصفة (المنتج، اسم الوصفة، كمية الإنتاج)
   - جدول فرعي لمكونات الوصفة
   - حساب تكلفة الوصفة تلقائياً
   - أزرار (جديد، حفظ، نسخ، تفعيل/إلغاء تفعيل)

9. نموذج حركات المخزون (فرم_حركات_المخزون):
   - فلاتر البحث (المادة، المخزن، نوع الحركة، التاريخ)
   - جدول لعرض الحركات
   - إجماليات (إجمالي الدخول، الخروج، الرصيد)
   - أزرار (بحث، تصدير، طباعة)

=====================================================
تصميم نماذج التقارير
=====================================================

10. نموذج تقارير المخزون (فرم_تقارير_المخزون):
    - خيارات التقرير (كامل، حسب المجموعة، حسب المخزن)
    - فلاتر التاريخ
    - خيارات العرض (تفصيلي، إجمالي)
    - أزرار (عرض، طباعة، تصدير)

11. نموذج تقارير التكاليف (فرم_تقارير_التكاليف):
    - خيارات التقرير (حسب المنتج، حسب أمر الإنتاج، حسب الفترة)
    - فلاتر التاريخ والمنتج
    - مقارنة التكلفة المخططة مع الفعلية
    - أزرار (عرض، طباعة، تصدير)

12. نموذج تقارير الإنتاج (فرم_تقارير_الإنتاج):
    - خيارات التقرير (حسب المنتج، حسب الفترة، حسب الحالة)
    - إحصائيات الإنتاج (الكمية المخططة، المنتجة، نسبة الإنجاز)
    - أزرار (عرض، طباعة، تصدير)

=====================================================
تصميم نماذج المساعدة
=====================================================

13. نموذج البحث السريع (فرم_البحث_السريع):
    - حقل بحث موحد
    - نتائج البحث في جداول متعددة
    - إمكانية الانتقال المباشر للسجل

14. نموذج النسخ الاحتياطي (فرم_النسخ_الاحتياطي):
    - خيارات النسخ الاحتياطي
    - جدولة النسخ التلقائي
    - استعادة النسخ الاحتياطية

15. نموذج إعدادات النظام (فرم_إعدادات_النظام):
    - إعدادات عامة (اسم الشركة، العنوان، الشعار)
    - إعدادات المخزون (طريقة التقييم، تنبيهات الحد الأدنى)
    - إعدادات التكاليف (نسب التكاليف غير المباشرة)
    - إعدادات المستخدمين والصلاحيات

=====================================================
خصائص التصميم العامة
=====================================================

1. التصميم باللغة العربية:
   - جميع التسميات والعناوين باللغة العربية
   - محاذاة النصوص من اليمين لليسار
   - استخدام خطوط عربية واضحة (Tahoma, Arial Unicode MS)

2. سهولة الاستخدام:
   - ألوان متناسقة ومريحة للعين
   - أزرار واضحة ومفهومة
   - رسائل تأكيد وتحذير مناسبة
   - اختصارات لوحة المفاتيح

3. التحقق من صحة البيانات:
   - التحقق من الحقول المطلوبة
   - التحقق من صحة التواريخ والأرقام
   - منع إدخال بيانات مكررة
   - رسائل خطأ واضحة ومفيدة

4. الأمان:
   - تسجيل دخول المستخدمين
   - صلاحيات مختلفة حسب نوع المستخدم
   - تسجيل العمليات والتغييرات
   - حماية من الحذف غير المقصود

5. الأداء:
   - فهرسة الحقول المهمة
   - تحسين الاستعلامات
   - تحميل البيانات حسب الحاجة
   - ضغط قاعدة البيانات دورياً

=====================================================
إرشادات التطبيق
=====================================================

1. ترتيب إنشاء النماذج:
   أ. النماذج الأساسية (المواد، المنتجات، الموردين، المخازن)
   ب. نماذج الإعداد (الوصفات، مراكز التكلفة)
   ج. نماذج المعاملات (فواتير الشراء، أوامر الإنتاج)
   د. نماذج التقارير والاستعلامات

2. اختبار النماذج:
   - اختبار إدخال البيانات
   - اختبار العمليات الحسابية
   - اختبار التحقق من صحة البيانات
   - اختبار الطباعة والتصدير

3. التدريب:
   - إعداد دليل المستخدم
   - تدريب المستخدمين على النظام
   - إعداد بيانات تجريبية للتدريب
