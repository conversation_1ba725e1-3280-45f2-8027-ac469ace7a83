-- نظام إدارة تصنيع المعمول بالجوز
-- تصميم قاعدة البيانات الشاملة
-- تاريخ الإنشاء: 2025-09-19
-- المطور: نظام إدارة التصنيع الاحترافي

-- =====================================================
-- الجداول الأساسية (Master Data Tables)
-- =====================================================

-- جدول وحدات القياس
CREATE TABLE وحدات_القياس (
    رقم_الوحدة AUTOINCREMENT PRIMARY KEY,
    اسم_الوحدة TEXT(50) NOT NULL UNIQUE,
    رمز_الوحدة TEXT(10) NOT NULL UNIQUE,
    وصف_الوحدة TEXT(100),
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- جدول المخازن
CREATE TABLE المخازن (
    رقم_المخزن AUTOINCREMENT PRIMARY KEY,
    اسم_المخزن TEXT(100) NOT NULL UNIQUE,
    موقع_المخزن TEXT(200),
    مسؤول_المخزن TEXT(100),
    هاتف_المسؤول TEXT(20),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- جدول الموردين
CREATE TABLE الموردين (
    رقم_المورد AUTOINCREMENT PRIMARY KEY,
    اسم_المورد TEXT(100) NOT NULL,
    اسم_جهة_الاتصال TEXT(100),
    عنوان_المورد TEXT(200),
    هاتف_المورد TEXT(20),
    فاكس_المورد TEXT(20),
    ايميل_المورد TEXT(100),
    الرقم_الضريبي TEXT(50),
    شروط_الدفع TEXT(100),
    حد_الائتمان CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- جدول العملاء
CREATE TABLE العملاء (
    رقم_العميل AUTOINCREMENT PRIMARY KEY,
    اسم_العميل TEXT(100) NOT NULL,
    اسم_جهة_الاتصال TEXT(100),
    عنوان_العميل TEXT(200),
    هاتف_العميل TEXT(20),
    فاكس_العميل TEXT(20),
    ايميل_العميل TEXT(100),
    الرقم_الضريبي TEXT(50),
    شروط_الدفع TEXT(100),
    حد_الائتمان CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- جدول مراكز التكلفة
CREATE TABLE مراكز_التكلفة (
    رقم_مركز_التكلفة AUTOINCREMENT PRIMARY KEY,
    اسم_مركز_التكلفة TEXT(100) NOT NULL UNIQUE,
    وصف_مركز_التكلفة TEXT(200),
    مسؤول_المركز TEXT(100),
    نوع_المركز TEXT(50), -- إنتاج، خدمات، إداري
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True
);

-- جدول المواد الخام
CREATE TABLE المواد_الخام (
    رقم_المادة AUTOINCREMENT PRIMARY KEY,
    كود_المادة TEXT(20) NOT NULL UNIQUE,
    اسم_المادة TEXT(100) NOT NULL,
    وصف_المادة TEXT(200),
    رقم_وحدة_القياس LONG,
    الحد_الأدنى DOUBLE DEFAULT 0,
    الحد_الأقصى DOUBLE DEFAULT 0,
    نقطة_إعادة_الطلب DOUBLE DEFAULT 0,
    سعر_الشراء_الافتراضي CURRENCY DEFAULT 0,
    مجموعة_المادة TEXT(50), -- دقيق، سكر، زيوت، مكسرات، إلخ
    رقم_المورد_الافتراضي LONG,
    مدة_الصلاحية INTEGER, -- بالأيام
    طريقة_التخزين TEXT(100),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True,
    FOREIGN KEY (رقم_وحدة_القياس) REFERENCES وحدات_القياس(رقم_الوحدة),
    FOREIGN KEY (رقم_المورد_الافتراضي) REFERENCES الموردين(رقم_المورد)
);

-- جدول المنتجات التامة
CREATE TABLE المنتجات_التامة (
    رقم_المنتج AUTOINCREMENT PRIMARY KEY,
    كود_المنتج TEXT(20) NOT NULL UNIQUE,
    اسم_المنتج TEXT(100) NOT NULL,
    وصف_المنتج TEXT(200),
    رقم_وحدة_القياس LONG,
    الوزن_الصافي DOUBLE, -- بالجرام
    مدة_الصلاحية INTEGER, -- بالأيام
    سعر_البيع_المقترح CURRENCY DEFAULT 0,
    تكلفة_التصنيع_المتوقعة CURRENCY DEFAULT 0,
    مجموعة_المنتج TEXT(50), -- معمول جوز، معمول تمر، إلخ
    طريقة_التعبئة TEXT(100),
    طريقة_التخزين TEXT(100),
    ملاحظات MEMO,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    نشط YES/NO DEFAULT True,
    FOREIGN KEY (رقم_وحدة_القياس) REFERENCES وحدات_القياس(رقم_الوحدة)
);

-- =====================================================
-- جداول المعاملات (Transaction Tables)
-- =====================================================

-- جدول فواتير الشراء
CREATE TABLE فواتير_الشراء (
    رقم_الفاتورة AUTOINCREMENT PRIMARY KEY,
    رقم_فاتورة_المورد TEXT(50),
    رقم_المورد LONG NOT NULL,
    تاريخ_الفاتورة DATETIME NOT NULL,
    تاريخ_الاستحقاق DATETIME,
    رقم_المخزن LONG NOT NULL,
    إجمالي_الفاتورة CURRENCY DEFAULT 0,
    قيمة_الضريبة CURRENCY DEFAULT 0,
    نسبة_الضريبة DOUBLE DEFAULT 0,
    إجمالي_شامل_الضريبة CURRENCY DEFAULT 0,
    قيمة_الخصم CURRENCY DEFAULT 0,
    الصافي_بعد_الخصم CURRENCY DEFAULT 0,
    حالة_الفاتورة TEXT(20) DEFAULT 'مسودة', -- مسودة، معتمدة، ملغاة
    ملاحظات MEMO,
    تاريخ_الإدخال DATETIME DEFAULT Now(),
    مستخدم_الإدخال TEXT(50),
    FOREIGN KEY (رقم_المورد) REFERENCES الموردين(رقم_المورد),
    FOREIGN KEY (رقم_المخزن) REFERENCES المخازن(رقم_المخزن)
);

-- جدول تفاصيل فواتير الشراء
CREATE TABLE تفاصيل_فواتير_الشراء (
    رقم_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_الفاتورة LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية DOUBLE NOT NULL,
    سعر_الوحدة CURRENCY NOT NULL,
    إجمالي_السطر CURRENCY NOT NULL,
    نسبة_الخصم DOUBLE DEFAULT 0,
    قيمة_الخصم CURRENCY DEFAULT 0,
    الصافي_بعد_الخصم CURRENCY NOT NULL,
    تاريخ_الصلاحية DATETIME,
    رقم_اللوط TEXT(50),
    ملاحظات TEXT(200),
    FOREIGN KEY (رقم_الفاتورة) REFERENCES فواتير_الشراء(رقم_الفاتورة),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة)
);

-- جدول الوصفات
CREATE TABLE الوصفات (
    رقم_الوصفة AUTOINCREMENT PRIMARY KEY,
    رقم_المنتج LONG NOT NULL,
    اسم_الوصفة TEXT(100) NOT NULL,
    وصف_الوصفة TEXT(200),
    كمية_الإنتاج DOUBLE NOT NULL, -- الكمية المنتجة من هذه الوصفة
    رقم_وحدة_الإنتاج LONG,
    تكلفة_الأيدي_العاملة_للوحدة CURRENCY DEFAULT 0,
    تكلفة_التشغيل_للوحدة CURRENCY DEFAULT 0,
    نسبة_التكاليف_غير_المباشرة DOUBLE DEFAULT 0, -- كنسبة مئوية
    إصدار_الوصفة TEXT(10) DEFAULT '1.0',
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_آخر_تعديل DATETIME DEFAULT Now(),
    نشطة YES/NO DEFAULT True,
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات_التامة(رقم_المنتج),
    FOREIGN KEY (رقم_وحدة_الإنتاج) REFERENCES وحدات_القياس(رقم_الوحدة)
);

-- جدول تفاصيل الوصفات
CREATE TABLE تفاصيل_الوصفات (
    رقم_تفصيل_الوصفة AUTOINCREMENT PRIMARY KEY,
    رقم_الوصفة LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    رقم_وحدة_القياس LONG,
    نسبة_الفاقد DOUBLE DEFAULT 0, -- نسبة الفاقد المتوقع
    ملاحظات TEXT(200),
    ترتيب_الإضافة INTEGER, -- ترتيب إضافة المادة في عملية التصنيع
    FOREIGN KEY (رقم_الوصفة) REFERENCES الوصفات(رقم_الوصفة),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة),
    FOREIGN KEY (رقم_وحدة_القياس) REFERENCES وحدات_القياس(رقم_الوحدة)
);

-- جدول أوامر الإنتاج
CREATE TABLE أوامر_الإنتاج (
    رقم_أمر_الإنتاج AUTOINCREMENT PRIMARY KEY,
    رقم_المنتج LONG NOT NULL,
    رقم_الوصفة LONG NOT NULL,
    الكمية_المطلوبة DOUBLE NOT NULL,
    تاريخ_الأمر DATETIME DEFAULT Now(),
    تاريخ_البدء_المخطط DATETIME,
    تاريخ_الانتهاء_المخطط DATETIME,
    تاريخ_البدء_الفعلي DATETIME,
    تاريخ_الانتهاء_الفعلي DATETIME,
    رقم_مركز_التكلفة LONG,
    رقم_المخزن_الإنتاج LONG NOT NULL,
    حالة_الأمر TEXT(20) DEFAULT 'مخطط', -- مخطط، قيد التنفيذ، مكتمل، ملغى
    الكمية_المنتجة_فعلياً DOUBLE DEFAULT 0,
    تكلفة_المواد_الخام CURRENCY DEFAULT 0,
    تكلفة_الأيدي_العاملة CURRENCY DEFAULT 0,
    التكاليف_المباشرة CURRENCY DEFAULT 0,
    التكاليف_غير_المباشرة CURRENCY DEFAULT 0,
    إجمالي_التكلفة CURRENCY DEFAULT 0,
    تكلفة_الوحدة CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    مستخدم_الإدخال TEXT(50),
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات_التامة(رقم_المنتج),
    FOREIGN KEY (رقم_الوصفة) REFERENCES الوصفات(رقم_الوصفة),
    FOREIGN KEY (رقم_مركز_التكلفة) REFERENCES مراكز_التكلفة(رقم_مركز_التكلفة),
    FOREIGN KEY (رقم_المخزن_الإنتاج) REFERENCES المخازن(رقم_المخزن)
);

-- جدول حركات المخزون
CREATE TABLE حركات_المخزون (
    رقم_الحركة AUTOINCREMENT PRIMARY KEY,
    رقم_المادة LONG NOT NULL,
    رقم_المخزن LONG NOT NULL,
    نوع_الحركة TEXT(20) NOT NULL, -- دخول، خروج، تحويل، جرد، تسوية
    مصدر_الحركة TEXT(20), -- فاتورة_شراء، أمر_إنتاج، مبيعات، تحويل، جرد
    رقم_المصدر LONG, -- رقم الفاتورة أو أمر الإنتاج
    الكمية DOUBLE NOT NULL,
    سعر_الوحدة CURRENCY,
    إجمالي_القيمة CURRENCY,
    الرصيد_قبل_الحركة DOUBLE DEFAULT 0,
    الرصيد_بعد_الحركة DOUBLE DEFAULT 0,
    المتوسط_المرجح_قبل CURRENCY DEFAULT 0,
    المتوسط_المرجح_بعد CURRENCY DEFAULT 0,
    تاريخ_الحركة DATETIME DEFAULT Now(),
    تاريخ_الصلاحية DATETIME,
    رقم_اللوط TEXT(50),
    ملاحظات TEXT(200),
    مستخدم_الإدخال TEXT(50),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة),
    FOREIGN KEY (رقم_المخزن) REFERENCES المخازن(رقم_المخزن)
);

-- جدول أرصدة المخزون
CREATE TABLE أرصدة_المخزون (
    رقم_الرصيد AUTOINCREMENT PRIMARY KEY,
    رقم_المادة LONG NOT NULL,
    رقم_المخزن LONG NOT NULL,
    الكمية_المتاحة DOUBLE DEFAULT 0,
    الكمية_المحجوزة DOUBLE DEFAULT 0,
    الكمية_الصافية DOUBLE DEFAULT 0,
    المتوسط_المرجح CURRENCY DEFAULT 0,
    إجمالي_القيمة CURRENCY DEFAULT 0,
    تاريخ_آخر_حركة DATETIME,
    تاريخ_آخر_تحديث DATETIME DEFAULT Now(),
    UNIQUE (رقم_المادة, رقم_المخزن),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة),
    FOREIGN KEY (رقم_المخزن) REFERENCES المخازن(رقم_المخزن)
);

-- جدول تكاليف الأيدي العاملة
CREATE TABLE تكاليف_الأيدي_العاملة (
    رقم_التكلفة AUTOINCREMENT PRIMARY KEY,
    رقم_أمر_الإنتاج LONG NOT NULL,
    اسم_العامل TEXT(100),
    نوع_العمل TEXT(50), -- تحضير، خلط، تشكيل، خبز، تعبئة
    عدد_الساعات DOUBLE,
    معدل_الساعة CURRENCY,
    إجمالي_التكلفة CURRENCY,
    تاريخ_العمل DATETIME,
    ملاحظات TEXT(200),
    FOREIGN KEY (رقم_أمر_الإنتاج) REFERENCES أوامر_الإنتاج(رقم_أمر_الإنتاج)
);

-- جدول التكاليف المباشرة
CREATE TABLE التكاليف_المباشرة (
    رقم_التكلفة AUTOINCREMENT PRIMARY KEY,
    رقم_أمر_الإنتاج LONG NOT NULL,
    نوع_التكلفة TEXT(50), -- كهرباء، غاز، مياه، صيانة
    وصف_التكلفة TEXT(200),
    قيمة_التكلفة CURRENCY,
    تاريخ_التكلفة DATETIME,
    ملاحظات TEXT(200),
    FOREIGN KEY (رقم_أمر_الإنتاج) REFERENCES أوامر_الإنتاج(رقم_أمر_الإنتاج)
);

-- جدول التكاليف غير المباشرة
CREATE TABLE التكاليف_غير_المباشرة (
    رقم_التكلفة AUTOINCREMENT PRIMARY KEY,
    رقم_مركز_التكلفة LONG NOT NULL,
    نوع_التكلفة TEXT(50), -- إيجار، إدارة، تأمين، استهلاك
    وصف_التكلفة TEXT(200),
    قيمة_التكلفة CURRENCY,
    الفترة_من DATETIME,
    الفترة_إلى DATETIME,
    طريقة_التوزيع TEXT(50), -- حسب_الساعات، حسب_الكمية، حسب_التكلفة
    ملاحظات TEXT(200),
    FOREIGN KEY (رقم_مركز_التكلفة) REFERENCES مراكز_التكلفة(رقم_مركز_التكلفة)
);

-- جدول توزيع التكاليف غير المباشرة
CREATE TABLE توزيع_التكاليف_غير_المباشرة (
    رقم_التوزيع AUTOINCREMENT PRIMARY KEY,
    رقم_التكلفة LONG NOT NULL,
    رقم_أمر_الإنتاج LONG NOT NULL,
    نسبة_التوزيع DOUBLE,
    قيمة_التوزيع CURRENCY,
    تاريخ_التوزيع DATETIME DEFAULT Now(),
    FOREIGN KEY (رقم_التكلفة) REFERENCES التكاليف_غير_المباشرة(رقم_التكلفة),
    FOREIGN KEY (رقم_أمر_الإنتاج) REFERENCES أوامر_الإنتاج(رقم_أمر_الإنتاج)
);

-- =====================================================
-- جداول المبيعات (اختيارية)
-- =====================================================

-- جدول فواتير المبيعات
CREATE TABLE فواتير_المبيعات (
    رقم_فاتورة_المبيعات AUTOINCREMENT PRIMARY KEY,
    رقم_العميل LONG NOT NULL,
    تاريخ_الفاتورة DATETIME NOT NULL,
    تاريخ_الاستحقاق DATETIME,
    رقم_المخزن LONG NOT NULL,
    إجمالي_الفاتورة CURRENCY DEFAULT 0,
    قيمة_الضريبة CURRENCY DEFAULT 0,
    نسبة_الضريبة DOUBLE DEFAULT 0,
    إجمالي_شامل_الضريبة CURRENCY DEFAULT 0,
    قيمة_الخصم CURRENCY DEFAULT 0,
    الصافي_بعد_الخصم CURRENCY DEFAULT 0,
    حالة_الفاتورة TEXT(20) DEFAULT 'مسودة',
    ملاحظات MEMO,
    تاريخ_الإدخال DATETIME DEFAULT Now(),
    مستخدم_الإدخال TEXT(50),
    FOREIGN KEY (رقم_العميل) REFERENCES العملاء(رقم_العميل),
    FOREIGN KEY (رقم_المخزن) REFERENCES المخازن(رقم_المخزن)
);

-- جدول تفاصيل فواتير المبيعات
CREATE TABLE تفاصيل_فواتير_المبيعات (
    رقم_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_فاتورة_المبيعات LONG NOT NULL,
    رقم_المنتج LONG NOT NULL,
    الكمية DOUBLE NOT NULL,
    سعر_الوحدة CURRENCY NOT NULL,
    إجمالي_السطر CURRENCY NOT NULL,
    نسبة_الخصم DOUBLE DEFAULT 0,
    قيمة_الخصم CURRENCY DEFAULT 0,
    الصافي_بعد_الخصم CURRENCY NOT NULL,
    تكلفة_الوحدة CURRENCY DEFAULT 0,
    إجمالي_التكلفة CURRENCY DEFAULT 0,
    هامش_الربح CURRENCY DEFAULT 0,
    ملاحظات TEXT(200),
    FOREIGN KEY (رقم_فاتورة_المبيعات) REFERENCES فواتير_المبيعات(رقم_فاتورة_المبيعات),
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات_التامة(رقم_المنتج)
);

-- =====================================================
-- الفهارس لتحسين الأداء
-- =====================================================

-- فهارس جدول حركات المخزون
CREATE INDEX idx_حركات_المخزون_المادة ON حركات_المخزون(رقم_المادة);
CREATE INDEX idx_حركات_المخزون_المخزن ON حركات_المخزون(رقم_المخزن);
CREATE INDEX idx_حركات_المخزون_التاريخ ON حركات_المخزون(تاريخ_الحركة);
CREATE INDEX idx_حركات_المخزون_النوع ON حركات_المخزون(نوع_الحركة);

-- فهارس جدول أرصدة المخزون
CREATE INDEX idx_أرصدة_المخزون_المادة ON أرصدة_المخزون(رقم_المادة);
CREATE INDEX idx_أرصدة_المخزون_المخزن ON أرصدة_المخزون(رقم_المخزن);

-- فهارس جدول فواتير الشراء
CREATE INDEX idx_فواتير_الشراء_المورد ON فواتير_الشراء(رقم_المورد);
CREATE INDEX idx_فواتير_الشراء_التاريخ ON فواتير_الشراء(تاريخ_الفاتورة);
CREATE INDEX idx_فواتير_الشراء_الحالة ON فواتير_الشراء(حالة_الفاتورة);

-- فهارس جدول أوامر الإنتاج
CREATE INDEX idx_أوامر_الإنتاج_المنتج ON أوامر_الإنتاج(رقم_المنتج);
CREATE INDEX idx_أوامر_الإنتاج_التاريخ ON أوامر_الإنتاج(تاريخ_الأمر);
CREATE INDEX idx_أوامر_الإنتاج_الحالة ON أوامر_الإنتاج(حالة_الأمر);

-- =====================================================
-- البيانات الأولية
-- =====================================================

-- إدخال وحدات القياس الأساسية
INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, وصف_الوحدة) VALUES
('كيلو جرام', 'كجم', 'وحدة قياس الوزن الأساسية'),
('جرام', 'جم', 'وحدة قياس الوزن الفرعية'),
('لتر', 'لتر', 'وحدة قياس السوائل'),
('مليلتر', 'مل', 'وحدة قياس السوائل الفرعية'),
('قطعة', 'قطعة', 'وحدة العد'),
('علبة', 'علبة', 'وحدة التعبئة'),
('كيس', 'كيس', 'وحدة التعبئة'),
('صندوق', 'صندوق', 'وحدة التعبئة الكبيرة');

-- إدخال المخازن الأساسية
INSERT INTO المخازن (اسم_المخزن, موقع_المخزن, مسؤول_المخزن) VALUES
('مخزن المواد الخام', 'الطابق الأرضي - قسم أ', 'أحمد محمد'),
('مخزن المنتجات التامة', 'الطابق الأرضي - قسم ب', 'فاطمة علي'),
('مخزن التعبئة والتغليف', 'الطابق الأول', 'محمد حسن'),
('مخزن الاحتياطي', 'المستودع الخارجي', 'علي أحمد');

-- إدخال مراكز التكلفة
INSERT INTO مراكز_التكلفة (اسم_مركز_التكلفة, وصف_مركز_التكلفة, نوع_المركز) VALUES
('قسم التحضير والخلط', 'تحضير وخلط المواد الخام', 'إنتاج'),
('قسم التشكيل', 'تشكيل المعمول', 'إنتاج'),
('قسم الخبز', 'خبز المنتجات', 'إنتاج'),
('قسم التعبئة والتغليف', 'تعبئة وتغليف المنتجات النهائية', 'إنتاج'),
('الإدارة العامة', 'التكاليف الإدارية العامة', 'إداري'),
('قسم الصيانة', 'صيانة المعدات والآلات', 'خدمات');

-- إدخال المواد الخام الأساسية لصنع المعمول بالجوز
INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, وصف_المادة, رقم_وحدة_القياس, مجموعة_المادة, الحد_الأدنى, نقطة_إعادة_الطلب) VALUES
('دقيق001', 'دقيق أبيض فاخر', 'دقيق قمح أبيض درجة أولى', 1, 'دقيق', 50, 100),
('سكر001', 'سكر أبيض ناعم', 'سكر أبيض ناعم درجة أولى', 1, 'سكر', 25, 50),
('زبدة001', 'زبدة طبيعية', 'زبدة بقري طبيعية', 1, 'دهون', 10, 20),
('جوز001', 'جوز مقشر', 'جوز مقشر ومنظف', 1, 'مكسرات', 15, 30),
('بيض001', 'بيض طازج', 'بيض دجاج طازج', 5, 'بيض', 100, 200),
('فانيليا001', 'خلاصة الفانيليا', 'خلاصة فانيليا طبيعية', 4, 'نكهات', 1, 2),
('بيكنج001', 'بيكنج باودر', 'مسحوق الخبز', 2, 'مواد مساعدة', 2, 5),
('ملح001', 'ملح طعام', 'ملح طعام ناعم', 1, 'توابل', 5, 10),
('حليب001', 'حليب بودرة', 'حليب بودرة كامل الدسم', 1, 'ألبان', 10, 20),
('سمن001', 'سمن نباتي', 'سمن نباتي للخبز', 1, 'دهون', 15, 30);

-- إدخال المنتجات التامة
INSERT INTO المنتجات_التامة (كود_المنتج, اسم_المنتج, وصف_المنتج, رقم_وحدة_القياس, الوزن_الصافي, مدة_الصلاحية, مجموعة_المنتج) VALUES
('معمول001', 'معمول جوز صغير', 'معمول محشو بالجوز - حجم صغير', 5, 25, 30, 'معمول جوز'),
('معمول002', 'معمول جوز متوسط', 'معمول محشو بالجوز - حجم متوسط', 5, 40, 30, 'معمول جوز'),
('معمول003', 'معمول جوز كبير', 'معمول محشو بالجوز - حجم كبير', 5, 60, 30, 'معمول جوز'),
('معمول004', 'معمول جوز علبة هدايا', 'معمول جوز في علبة هدايا فاخرة', 6, 500, 45, 'معمول جوز');

-- =====================================================
-- وصفة المعمول بالجوز الأساسية
-- =====================================================

-- إدخال الوصفة الأساسية للمعمول الصغير
INSERT INTO الوصفات (رقم_المنتج, اسم_الوصفة, وصف_الوصفة, كمية_الإنتاج, رقم_وحدة_الإنتاج, تكلفة_الأيدي_العاملة_للوحدة, تكلفة_التشغيل_للوحدة, نسبة_التكاليف_غير_المباشرة) VALUES
(1, 'وصفة المعمول الصغير الأساسية', 'الوصفة الأساسية لإنتاج 100 قطعة معمول صغير', 100, 5, 0.50, 0.25, 15.0);

-- إدخال تفاصيل الوصفة للمعمول الصغير (100 قطعة)
INSERT INTO تفاصيل_الوصفات (رقم_الوصفة, رقم_المادة, الكمية_المطلوبة, رقم_وحدة_القياس, نسبة_الفاقد, ترتيب_الإضافة) VALUES
(1, 1, 2.0, 1, 2.0, 1),    -- 2 كيلو دقيق
(1, 2, 0.8, 1, 1.0, 2),    -- 800 جرام سكر
(1, 3, 0.6, 1, 0.5, 3),    -- 600 جرام زبدة
(1, 4, 1.2, 1, 3.0, 4),    -- 1.2 كيلو جوز
(1, 5, 6, 5, 5.0, 5),      -- 6 بيضات
(1, 6, 0.02, 3, 0, 6),     -- 20 مل فانيليا
(1, 7, 0.015, 1, 0, 7),    -- 15 جرام بيكنج باودر
(1, 8, 0.005, 1, 0, 8),    -- 5 جرام ملح
(1, 9, 0.1, 1, 0, 9),      -- 100 جرام حليب بودرة
(1, 10, 0.3, 1, 1.0, 10);  -- 300 جرام سمن
