# إنشاء قاعدة بيانات Access بالصيغة الصحيحة
Write-Host "إنشاء قاعدة بيانات Access..." -ForegroundColor Green

$dbPath = "نظام_المعمول.accdb"
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force
}

try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    $access.NewCurrentDatabase($dbPath)
    
    Write-Host "تم إنشاء قاعدة البيانات: $dbPath" -ForegroundColor Green
    
    # إنشاء جدول وحدات القياس
    $access.DoCmd.RunSQL("CREATE TABLE وحدات_القياس (
        رقم_الوحدة COUNTER PRIMARY KEY,
        اسم_الوحدة TEXT(50),
        رمز_الوحدة TEXT(10),
        نشطة YESNO
    )")
    
    # إنشاء جدول المخازن
    $access.DoCmd.RunSQL("CREATE TABLE المخازن (
        رقم_المخزن COUNTER PRIMARY KEY,
        اسم_المخزن TEXT(100),
        نوع_المخزن TEXT(50),
        نشط YESNO
    )")
    
    # إنشاء جدول الموردين
    $access.DoCmd.RunSQL("CREATE TABLE الموردين (
        رقم_المورد COUNTER PRIMARY KEY,
        اسم_المورد TEXT(100),
        هاتف_المورد TEXT(50),
        نشط YESNO
    )")
    
    # إنشاء جدول المواد الخام
    $access.DoCmd.RunSQL("CREATE TABLE المواد_الخام (
        رقم_المادة COUNTER PRIMARY KEY,
        كود_المادة TEXT(50),
        اسم_المادة TEXT(100),
        رقم_وحدة_القياس LONG,
        الحد_الأدنى DOUBLE,
        نشط YESNO
    )")
    
    # إنشاء جدول المنتجات التامة
    $access.DoCmd.RunSQL("CREATE TABLE المنتجات_التامة (
        رقم_المنتج COUNTER PRIMARY KEY,
        كود_المنتج TEXT(50),
        اسم_المنتج TEXT(100),
        رقم_وحدة_القياس LONG,
        سعر_البيع CURRENCY,
        نشط YESNO
    )")
    
    # إنشاء جدول فواتير الشراء
    $access.DoCmd.RunSQL("CREATE TABLE فواتير_الشراء (
        رقم_الفاتورة COUNTER PRIMARY KEY,
        رقم_فاتورة_المورد TEXT(50),
        رقم_المورد LONG,
        تاريخ_الفاتورة DATETIME,
        رقم_المخزن LONG,
        إجمالي_الفاتورة CURRENCY,
        حالة_الفاتورة TEXT(20)
    )")
    
    # إنشاء جدول تفاصيل فواتير الشراء
    $access.DoCmd.RunSQL("CREATE TABLE تفاصيل_فواتير_الشراء (
        رقم_التفصيل COUNTER PRIMARY KEY,
        رقم_الفاتورة LONG,
        رقم_المادة LONG,
        الكمية DOUBLE,
        سعر_الوحدة CURRENCY,
        إجمالي_السطر CURRENCY
    )")
    
    # إنشاء جدول أرصدة المخزون
    $access.DoCmd.RunSQL("CREATE TABLE أرصدة_المخزون (
        رقم_الرصيد COUNTER PRIMARY KEY,
        رقم_المادة LONG,
        رقم_المخزن LONG,
        الكمية_المتاحة DOUBLE,
        المتوسط_المرجح CURRENCY,
        إجمالي_القيمة CURRENCY
    )")
    
    # إنشاء جدول حركات المخزون
    $access.DoCmd.RunSQL("CREATE TABLE حركات_المخزون (
        رقم_الحركة COUNTER PRIMARY KEY,
        رقم_المادة LONG,
        رقم_المخزن LONG,
        نوع_الحركة TEXT(20),
        الكمية DOUBLE,
        سعر_الوحدة CURRENCY,
        تاريخ_الحركة DATETIME
    )")
    
    Write-Host "تم إنشاء الجداول بنجاح" -ForegroundColor Green
    
    # إدخال البيانات الأساسية
    Write-Host "إدخال البيانات الأساسية..." -ForegroundColor Yellow
    
    # وحدات القياس
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نشطة) VALUES ('كيلو جرام', 'كجم', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نشطة) VALUES ('جرام', 'جم', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نشطة) VALUES ('لتر', 'لتر', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نشطة) VALUES ('قطعة', 'قطعة', True)")
    
    # المخازن
    $access.DoCmd.RunSQL("INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, نشط) VALUES ('مخزن المواد الخام', 'مواد خام', True)")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, نشط) VALUES ('مخزن المنتجات التامة', 'منتجات تامة', True)")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, نشط) VALUES ('مخزن الإنتاج', 'إنتاج', True)")
    
    # الموردين
    $access.DoCmd.RunSQL("INSERT INTO الموردين (اسم_المورد, هاتف_المورد, نشط) VALUES ('شركة الدقيق المصري', '02-12345678', True)")
    $access.DoCmd.RunSQL("INSERT INTO الموردين (اسم_المورد, هاتف_المورد, نشط) VALUES ('مؤسسة المكسرات الذهبية', '02-87654321', True)")
    $access.DoCmd.RunSQL("INSERT INTO الموردين (اسم_المورد, هاتف_المورد, نشط) VALUES ('شركة السكر والحلويات', '02-11223344', True)")
    
    # المواد الخام
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, رقم_وحدة_القياس, الحد_الأدنى, نشط) VALUES ('DQ001', 'دقيق أبيض', 1, 50, True)")
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, رقم_وحدة_القياس, الحد_الأدنى, نشط) VALUES ('SG001', 'سكر أبيض', 1, 25, True)")
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, رقم_وحدة_القياس, الحد_الأدنى, نشط) VALUES ('ZB001', 'زبدة', 1, 10, True)")
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, رقم_وحدة_القياس, الحد_الأدنى, نشط) VALUES ('JZ001', 'جوز مقشر', 1, 15, True)")
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, رقم_وحدة_القياس, الحد_الأدنى, نشط) VALUES ('BC001', 'بيض', 4, 100, True)")
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, رقم_وحدة_القياس, الحد_الأدنى, نشط) VALUES ('VN001', 'فانيليا', 2, 5, True)")
    
    # المنتجات التامة
    $access.DoCmd.RunSQL("INSERT INTO المنتجات_التامة (كود_المنتج, اسم_المنتج, رقم_وحدة_القياس, سعر_البيع, نشط) VALUES ('MJ001', 'معمول جوز صغير', 4, 5.50, True)")
    $access.DoCmd.RunSQL("INSERT INTO المنتجات_التامة (كود_المنتج, اسم_المنتج, رقم_وحدة_القياس, سعر_البيع, نشط) VALUES ('MJ002', 'معمول جوز كبير', 4, 8.00, True)")
    $access.DoCmd.RunSQL("INSERT INTO المنتجات_التامة (كود_المنتج, اسم_المنتج, رقم_وحدة_القياس, سعر_البيع, نشط) VALUES ('MJ003', 'معمول جوز فاخر', 4, 12.00, True)")
    
    Write-Host "تم إدخال البيانات الأساسية بنجاح" -ForegroundColor Green
    
    # إنشاء فاتورة شراء تجريبية
    Write-Host "إنشاء فاتورة شراء تجريبية..." -ForegroundColor Yellow
    
    $access.DoCmd.RunSQL("INSERT INTO فواتير_الشراء (رقم_فاتورة_المورد, رقم_المورد, تاريخ_الفاتورة, رقم_المخزن, إجمالي_الفاتورة, حالة_الفاتورة) VALUES ('INV-001', 1, Date(), 1, 3500, 'معتمدة')")
    
    $access.DoCmd.RunSQL("INSERT INTO تفاصيل_فواتير_الشراء (رقم_الفاتورة, رقم_المادة, الكمية, سعر_الوحدة, إجمالي_السطر) VALUES (1, 1, 100, 25, 2500)")
    $access.DoCmd.RunSQL("INSERT INTO تفاصيل_فواتير_الشراء (رقم_الفاتورة, رقم_المادة, الكمية, سعر_الوحدة, إجمالي_السطر) VALUES (1, 2, 50, 20, 1000)")
    
    # إدخال أرصدة المخزون
    $access.DoCmd.RunSQL("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المخزن, الكمية_المتاحة, المتوسط_المرجح, إجمالي_القيمة) VALUES (1, 1, 100, 25, 2500)")
    $access.DoCmd.RunSQL("INSERT INTO أرصدة_المخزون (رقم_المادة, رقم_المخزن, الكمية_المتاحة, المتوسط_المرجح, إجمالي_القيمة) VALUES (2, 1, 50, 20, 1000)")
    
    # إدخال حركات المخزون
    $access.DoCmd.RunSQL("INSERT INTO حركات_المخزون (رقم_المادة, رقم_المخزن, نوع_الحركة, الكمية, سعر_الوحدة, تاريخ_الحركة) VALUES (1, 1, 'دخول', 100, 25, Date())")
    $access.DoCmd.RunSQL("INSERT INTO حركات_المخزون (رقم_المادة, رقم_المخزن, نوع_الحركة, الكمية, سعر_الوحدة, تاريخ_الحركة) VALUES (2, 1, 'دخول', 50, 20, Date())")
    
    Write-Host "تم إنشاء البيانات التجريبية بنجاح" -ForegroundColor Green
    
    # إنشاء النماذج الأساسية
    Write-Host "إنشاء النماذج الأساسية..." -ForegroundColor Yellow
    
    # نموذج المواد الخام
    $access.DoCmd.OpenForm("المواد_الخام", 0, "", "", 1, 1)
    $access.DoCmd.Save(1, "فرم_المواد_الخام")
    $access.DoCmd.Close(1, "فرم_المواد_الخام")
    
    # نموذج فواتير الشراء
    $access.DoCmd.OpenForm("فواتير_الشراء", 0, "", "", 1, 1)
    $access.DoCmd.Save(1, "فرم_فواتير_الشراء")
    $access.DoCmd.Close(1, "فرم_فواتير_الشراء")
    
    Write-Host "تم إنشاء النماذج الأساسية بنجاح" -ForegroundColor Green
    
    # إنشاء التقارير الأساسية
    Write-Host "إنشاء التقارير الأساسية..." -ForegroundColor Yellow
    
    # تقرير المخزون
    $access.DoCmd.OpenReport("أرصدة_المخزون", 0, "", "", 1, "")
    $access.DoCmd.Save(3, "تقرير_المخزون")
    $access.DoCmd.Close(3, "تقرير_المخزون")
    
    Write-Host "تم إنشاء التقارير الأساسية بنجاح" -ForegroundColor Green
    
    # حفظ وإغلاق
    $access.CloseCurrentDatabase()
    $access.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    
    Write-Host "`n=== تم إنشاء قاعدة البيانات بنجاح ===" -ForegroundColor Green
    Write-Host "اسم الملف: $dbPath" -ForegroundColor Yellow
    Write-Host "`nالجداول المُنشأة:" -ForegroundColor Cyan
    Write-Host "  ✓ وحدات_القياس (4 وحدات)" -ForegroundColor White
    Write-Host "  ✓ المخازن (3 مخازن)" -ForegroundColor White
    Write-Host "  ✓ الموردين (3 موردين)" -ForegroundColor White
    Write-Host "  ✓ المواد_الخام (6 مواد)" -ForegroundColor White
    Write-Host "  ✓ المنتجات_التامة (3 منتجات)" -ForegroundColor White
    Write-Host "  ✓ فواتير_الشراء (فاتورة تجريبية)" -ForegroundColor White
    Write-Host "  ✓ تفاصيل_فواتير_الشراء" -ForegroundColor White
    Write-Host "  ✓ أرصدة_المخزون (أرصدة تجريبية)" -ForegroundColor White
    Write-Host "  ✓ حركات_المخزون (حركات تجريبية)" -ForegroundColor White
    Write-Host "`nالنماذج المُنشأة:" -ForegroundColor Cyan
    Write-Host "  ✓ فرم_المواد_الخام" -ForegroundColor White
    Write-Host "  ✓ فرم_فواتير_الشراء" -ForegroundColor White
    Write-Host "`nالتقارير المُنشأة:" -ForegroundColor Cyan
    Write-Host "  ✓ تقرير_المخزون" -ForegroundColor White
    
    # فتح قاعدة البيانات
    Write-Host "`nفتح قاعدة البيانات..." -ForegroundColor Green
    Start-Process "msaccess.exe" -ArgumentList "`"$dbPath`""
    
} catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
    if ($access) {
        try {
            $access.CloseCurrentDatabase()
            $access.Quit()
        } catch {}
    }
}

Write-Host "`nقاعدة البيانات جاهزة للاستخدام!" -ForegroundColor Green
