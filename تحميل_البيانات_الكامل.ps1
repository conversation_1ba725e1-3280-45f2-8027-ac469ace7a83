# سكريبت تحميل البيانات الكامل لنظام إدارة تصنيع المعمول
# يجب تشغيله كمدير

Write-Host "بدء تحميل البيانات الكامل..." -ForegroundColor Green

# التحقق من وجود Microsoft Access
try {
    $access = New-Object -ComObject Access.Application
    Write-Host "تم العثور على Microsoft Access" -ForegroundColor Green
    $access.Quit()
} catch {
    Write-Host "خطأ: لم يتم العثور على Microsoft Access" -ForegroundColor Red
    exit 1
}

# إنشاء قاعدة بيانات جديدة إذا لم تكن موجودة
$dbPath = "نظام_إدارة_تصنيع_المعمول.accdb"
if (-not (Test-Path $dbPath)) {
    Write-Host "إنشاء قاعدة بيانات جديدة..." -ForegroundColor Yellow
    $access = New-Object -ComObject Access.Application
    $access.NewCurrentDatabase($dbPath)
    $access.CloseCurrentDatabase()
    $access.Quit()
}

# تحميل هيكل قاعدة البيانات
Write-Host "تحميل هيكل قاعدة البيانات..." -ForegroundColor Yellow
try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    $access.OpenCurrentDatabase($dbPath)
    
    # قراءة ملف SQL وتنفيذه
    $sqlContent = Get-Content "نظام_إدارة_تصنيع_المعمول.sql" -Raw -Encoding UTF8
    
    # تقسيم الأوامر
    $commands = $sqlContent -split "GO" | Where-Object { $_.Trim() -ne "" }
    
    foreach ($command in $commands) {
        $command = $command.Trim()
        if ($command -ne "" -and -not $command.StartsWith("--")) {
            try {
                $access.DoCmd.RunSQL($command)
                Write-Host "✓ تم تنفيذ أمر SQL" -ForegroundColor Green
            } catch {
                Write-Host "⚠ تحذير في تنفيذ أمر SQL: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
    }
    
    $access.CloseCurrentDatabase()
    $access.Quit()
    Write-Host "تم تحميل هيكل قاعدة البيانات بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في تحميل هيكل قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
}

# تحميل البيانات التجريبية
Write-Host "تحميل البيانات التجريبية..." -ForegroundColor Yellow
try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    $access.OpenCurrentDatabase($dbPath)
    
    # قراءة ملف البيانات التجريبية
    $testDataContent = Get-Content "اختبار_النظام_وبيانات_تجريبية.sql" -Raw -Encoding UTF8
    
    # تقسيم الأوامر
    $commands = $testDataContent -split ";" | Where-Object { $_.Trim() -ne "" }
    
    foreach ($command in $commands) {
        $command = $command.Trim()
        if ($command -ne "" -and -not $command.StartsWith("--") -and -not $command.StartsWith("SELECT")) {
            try {
                $access.DoCmd.RunSQL($command)
                Write-Host "✓ تم تحميل بيانات" -ForegroundColor Green
            } catch {
                Write-Host "⚠ تحذير في تحميل البيانات: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
    }
    
    $access.CloseCurrentDatabase()
    $access.Quit()
    Write-Host "تم تحميل البيانات التجريبية بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في تحميل البيانات التجريبية: $($_.Exception.Message)" -ForegroundColor Red
}

# تحميل الاستعلامات والتقارير
Write-Host "تحميل الاستعلامات والتقارير..." -ForegroundColor Yellow
try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    $access.OpenCurrentDatabase($dbPath)
    
    # قراءة ملف الاستعلامات
    $queriesContent = Get-Content "الاستعلامات_المحاسبية.sql" -Raw -Encoding UTF8
    
    # تقسيم الأوامر
    $commands = $queriesContent -split ";" | Where-Object { $_.Trim() -ne "" }
    
    foreach ($command in $commands) {
        $command = $command.Trim()
        if ($command -ne "" -and -not $command.StartsWith("--")) {
            try {
                $access.DoCmd.RunSQL($command)
                Write-Host "✓ تم إنشاء استعلام/تقرير" -ForegroundColor Green
            } catch {
                Write-Host "⚠ تحذير في إنشاء استعلام: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
    }
    
    $access.CloseCurrentDatabase()
    $access.Quit()
    Write-Host "تم تحميل الاستعلامات والتقارير بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في تحميل الاستعلامات: $($_.Exception.Message)" -ForegroundColor Red
}

# إنشاء النماذج الأساسية
Write-Host "إنشاء النماذج الأساسية..." -ForegroundColor Yellow
try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true  # إظهار Access لإنشاء النماذج
    $access.OpenCurrentDatabase($dbPath)
    
    # إنشاء نموذج الشاشة الرئيسية
    $mainForm = $access.CreateForm()
    $mainForm.Caption = "الشاشة الرئيسية - نظام إدارة تصنيع المعمول"
    $access.DoCmd.Save(1, "فرم_الشاشة_الرئيسية")
    $access.DoCmd.Close(1, "فرم_الشاشة_الرئيسية")
    
    # إنشاء نموذج المواد الخام
    $materialsForm = $access.CreateForm("المواد_الخام")
    $materialsForm.Caption = "إدارة المواد الخام"
    $access.DoCmd.Save(1, "فرم_المواد_الخام")
    $access.DoCmd.Close(1, "فرم_المواد_الخام")
    
    # إنشاء نموذج فواتير الشراء
    $purchaseForm = $access.CreateForm("فواتير_الشراء")
    $purchaseForm.Caption = "فواتير الشراء"
    $access.DoCmd.Save(1, "فرم_فواتير_الشراء")
    $access.DoCmd.Close(1, "فرم_فواتير_الشراء")
    
    # إنشاء نموذج أوامر الإنتاج
    $productionForm = $access.CreateForm("أوامر_الإنتاج")
    $productionForm.Caption = "أوامر الإنتاج"
    $access.DoCmd.Save(1, "فرم_أوامر_الإنتاج")
    $access.DoCmd.Close(1, "فرم_أوامر_الإنتاج")
    
    $access.CloseCurrentDatabase()
    $access.Quit()
    Write-Host "تم إنشاء النماذج الأساسية بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في إنشاء النماذج: $($_.Exception.Message)" -ForegroundColor Red
}

# إنشاء التقارير الأساسية
Write-Host "إنشاء التقارير الأساسية..." -ForegroundColor Yellow
try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true
    $access.OpenCurrentDatabase($dbPath)
    
    # إنشاء تقرير المخزون
    $inventoryReport = $access.CreateReport("تقرير_المخزون_الحالي")
    $inventoryReport.Caption = "تقرير المخزون الحالي"
    $access.DoCmd.Save(3, "تقرير_المخزون")
    $access.DoCmd.Close(3, "تقرير_المخزون")
    
    # إنشاء تقرير الإنتاج
    $productionReport = $access.CreateReport("تقرير_الإنتاج")
    $productionReport.Caption = "تقرير الإنتاج"
    $access.DoCmd.Save(3, "تقرير_الإنتاج")
    $access.DoCmd.Close(3, "تقرير_الإنتاج")
    
    $access.CloseCurrentDatabase()
    $access.Quit()
    Write-Host "تم إنشاء التقارير الأساسية بنجاح" -ForegroundColor Green
} catch {
    Write-Host "خطأ في إنشاء التقارير: $($_.Exception.Message)" -ForegroundColor Red
}

# تقرير نهائي
Write-Host "`n=== تقرير التحميل النهائي ===" -ForegroundColor Cyan
Write-Host "تم الانتهاء من تحميل جميع مكونات النظام" -ForegroundColor Green
Write-Host "يمكنك الآن فتح قاعدة البيانات: $dbPath" -ForegroundColor Yellow
Write-Host "للتحقق من المحتويات، افتح قاعدة البيانات في Microsoft Access" -ForegroundColor Yellow

# فتح قاعدة البيانات للمراجعة
Write-Host "`nفتح قاعدة البيانات للمراجعة..." -ForegroundColor Green
try {
    Start-Process "msaccess.exe" -ArgumentList "`"$dbPath`""
} catch {
    Write-Host "لم يتم فتح قاعدة البيانات تلقائياً. يرجى فتحها يدوياً." -ForegroundColor Yellow
}

Write-Host "`nانتهى السكريبت بنجاح!" -ForegroundColor Green
