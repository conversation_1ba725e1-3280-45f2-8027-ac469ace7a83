# تعليمات التشغيل النهائية - نظام إدارة تصنيع المعمول بالجوز

## 🚀 خطوات التشغيل السريع

### الخطوة 1: التحقق من المتطلبات
```powershell
# تحقق من وجود Microsoft Access
Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Access*"}
```

### الخطوة 2: تشغيل سكريبت الإنشاء
```powershell
# تشغيل كمدير
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\إنشاء_قاعدة_البيانات.ps1
```

### الخطوة 3: تحميل البيانات التجريبية
```sql
-- في Microsoft Access، تشغيل ملف
اختبار_النظام_وبيانات_تجريبية.sql
```

## 📋 قائمة التحقق النهائية

### ✅ الملفات المطلوبة
- [ ] `نظام_إدارة_تصنيع_المعمول.sql` - هيكل قاعدة البيانات
- [ ] `وحدات_VBA_نظام_المخزون.txt` - كود VBA
- [ ] `الاستعلامات_المحاسبية.sql` - الاستعلامات والتقارير
- [ ] `تصميم_النماذج_والعلاقات.txt` - تصميم النماذج
- [ ] `إنشاء_قاعدة_البيانات.ps1` - سكريبت التثبيت
- [ ] `اختبار_النظام_وبيانات_تجريبية.sql` - بيانات الاختبار
- [ ] `دليل_المستخدم.md` - دليل المستخدم
- [ ] `README.md` - وثائق المشروع

### ✅ الجداول الأساسية
- [ ] وحدات_القياس (8 وحدات)
- [ ] المخازن (4 مخازن)
- [ ] مراكز_التكلفة (6 مراكز)
- [ ] الموردين (5 موردين)
- [ ] المواد_الخام (10 مواد)
- [ ] المنتجات_التامة (4 منتجات)

### ✅ الجداول المتقدمة
- [ ] الوصفات (وصفة المعمول الأساسية)
- [ ] تفاصيل_الوصفات (10 مكونات)
- [ ] فواتير_الشراء
- [ ] تفاصيل_فواتير_الشراء
- [ ] أوامر_الإنتاج
- [ ] حركات_المخزون
- [ ] أرصدة_المخزون

### ✅ جداول التكاليف
- [ ] تكاليف_الأيدي_العاملة
- [ ] التكاليف_المباشرة
- [ ] التكاليف_غير_المباشرة
- [ ] توزيع_التكاليف_غير_المباشرة

### ✅ الاستعلامات والتقارير
- [ ] تقرير_المخزون_الحالي
- [ ] تقرير_حركات_المخزون
- [ ] تحليل_تكاليف_الوصفات
- [ ] تقرير_المشتريات
- [ ] تقرير_الإنتاج
- [ ] المواد_تحت_الحد_الأدنى

### ✅ وحدات VBA
- [ ] حساب_المتوسط_المرجح_الجديد
- [ ] تحديث_رصيد_المخزون
- [ ] إضافة_حركة_مخزون
- [ ] معالجة_فاتورة_شراء
- [ ] صرف_مواد_خام_لأمر_إنتاج

## 🔧 اختبار الوظائف الأساسية

### اختبار 1: إدخال فاتورة شراء
```sql
-- إدخال فاتورة جديدة
INSERT INTO فواتير_الشراء (رقم_فاتورة_المورد, رقم_المورد, تاريخ_الفاتورة, رقم_المخزن, حالة_الفاتورة) 
VALUES ('TEST-001', 1, Date(), 1, 'معتمدة');

-- إدخال تفاصيل الفاتورة
INSERT INTO تفاصيل_فواتير_الشراء (رقم_الفاتورة, رقم_المادة, الكمية, سعر_الوحدة, إجمالي_السطر, الصافي_بعد_الخصم) 
VALUES (LAST_INSERT_ID(), 1, 10, 25.00, 250.00, 250.00);

-- معالجة الفاتورة
CALL معالجة_فاتورة_شراء(LAST_INSERT_ID());
```

### اختبار 2: إنشاء أمر إنتاج
```sql
-- إنشاء أمر إنتاج جديد
INSERT INTO أوامر_الإنتاج (رقم_المنتج, رقم_الوصفة, الكمية_المطلوبة, تاريخ_الأمر, رقم_المخزن_الإنتاج, حالة_الأمر) 
VALUES (1, 1, 100, Date(), 1, 'قيد التنفيذ');

-- صرف المواد الخام
CALL صرف_مواد_خام_لأمر_إنتاج(LAST_INSERT_ID());
```

### اختبار 3: التقارير
```sql
-- اختبار تقرير المخزون
SELECT * FROM تقرير_المخزون_الحالي;

-- اختبار تقرير التكاليف
SELECT * FROM تحليل_تكاليف_الوصفات;

-- اختبار المواد تحت الحد الأدنى
SELECT * FROM المواد_تحت_الحد_الأدنى;
```

## 🎯 سيناريو اختبار شامل

### السيناريو: إنتاج 500 قطعة معمول جوز

#### الخطوة 1: التحقق من المخزون
```sql
SELECT م.اسم_المادة, ر.الكمية_المتاحة, م.الحد_الأدنى
FROM المواد_الخام م
INNER JOIN أرصدة_المخزون ر ON م.رقم_المادة = ر.رقم_المادة
WHERE ر.رقم_المخزن = 1;
```

#### الخطوة 2: شراء المواد الناقصة (إذا لزم الأمر)
```sql
-- إدخال فاتورة شراء للمواد الناقصة
-- (حسب نتائج الخطوة 1)
```

#### الخطوة 3: إنشاء أمر الإنتاج
```sql
INSERT INTO أوامر_الإنتاج (رقم_المنتج, رقم_الوصفة, الكمية_المطلوبة, تاريخ_الأمر, رقم_مركز_التكلفة, رقم_المخزن_الإنتاج, حالة_الأمر) 
VALUES (1, 1, 500, Date(), 1, 1, 'مخطط');
```

#### الخطوة 4: بدء التنفيذ
```sql
-- تغيير حالة الأمر
UPDATE أوامر_الإنتاج SET حالة_الأمر = 'قيد التنفيذ', تاريخ_البدء_الفعلي = Now() WHERE رقم_أمر_الإنتاج = [رقم_الأمر];

-- صرف المواد الخام
CALL صرف_مواد_خام_لأمر_إنتاج([رقم_الأمر]);
```

#### الخطوة 5: إدخال التكاليف
```sql
-- تكاليف الأيدي العاملة
INSERT INTO تكاليف_الأيدي_العاملة (رقم_أمر_الإنتاج, اسم_العامل, نوع_العمل, عدد_الساعات, معدل_الساعة, إجمالي_التكلفة, تاريخ_العمل) 
VALUES ([رقم_الأمر], 'أحمد محمد', 'تحضير وخلط', 8.0, 25.00, 200.00, Date());

-- التكاليف المباشرة
INSERT INTO التكاليف_المباشرة (رقم_أمر_الإنتاج, نوع_التكلفة, وصف_التكلفة, قيمة_التكلفة, تاريخ_التكلفة) 
VALUES ([رقم_الأمر], 'كهرباء', 'استهلاك الكهرباء', 300.00, Date());
```

#### الخطوة 6: إنهاء الإنتاج
```sql
-- تحديث الكمية المنتجة وإنهاء الأمر
UPDATE أوامر_الإنتاج SET 
    الكمية_المنتجة_فعلياً = 495,
    حالة_الأمر = 'مكتمل',
    تاريخ_الانتهاء_الفعلي = Now()
WHERE رقم_أمر_الإنتاج = [رقم_الأمر];
```

#### الخطوة 7: مراجعة النتائج
```sql
-- مراجعة تكلفة الإنتاج
SELECT * FROM استعلام_إجمالي_تكاليف_الإنتاج WHERE رقم_أمر_الإنتاج = [رقم_الأمر];

-- مراجعة حالة المخزون
SELECT * FROM تقرير_المخزون_الحالي;
```

## 📊 مؤشرات الأداء المتوقعة

### الأداء المالي
- **تكلفة الوحدة المتوقعة**: 3.50 - 4.00 جنيه
- **هامش الربح المستهدف**: 40-50%
- **سعر البيع المقترح**: 5.50 - 6.00 جنيه

### الأداء التشغيلي
- **زمن الإنتاج**: 2-3 أيام لـ 500 قطعة
- **نسبة الفاقد المتوقع**: 1-3%
- **كفاءة استخدام المواد**: 97-99%

### مؤشرات المخزون
- **معدل دوران المخزون**: 12-15 مرة سنوياً
- **أيام تغطية المخزون**: 24-30 يوم
- **نسبة المواد تحت الحد الأدنى**: أقل من 5%

## 🔍 نقاط المراجعة الدورية

### يومياً
- [ ] مراجعة أرصدة المخزون
- [ ] متابعة أوامر الإنتاج قيد التنفيذ
- [ ] إدخال فواتير الشراء الجديدة
- [ ] تسجيل تكاليف الأيدي العاملة

### أسبوعياً
- [ ] مراجعة تقرير المواد تحت الحد الأدنى
- [ ] تحليل تكاليف الإنتاج
- [ ] مراجعة كفاءة استخدام المواد
- [ ] متابعة أوامر الشراء المعلقة

### شهرياً
- [ ] مراجعة شاملة للتكاليف
- [ ] تحليل ربحية المنتجات
- [ ] تحديث أسعار المواد الخام
- [ ] مراجعة الوصفات وتحديثها

### ربع سنوياً
- [ ] تحليل الأداء المالي الشامل
- [ ] مراجعة وتحديث الحدود الدنيا للمخزون
- [ ] تقييم أداء الموردين
- [ ] تحديث نسب التكاليف غير المباشرة

## 🆘 استكشاف الأخطاء وحلها

### مشكلة: رصيد سالب في المخزون
**الحل:**
```sql
-- البحث عن الأرصدة السالبة
SELECT * FROM أرصدة_المخزون WHERE الكمية_المتاحة < 0;

-- مراجعة حركات المخزون
SELECT * FROM حركات_المخزون WHERE رقم_المادة = [رقم_المادة] ORDER BY تاريخ_الحركة;
```

### مشكلة: خطأ في حساب المتوسط المرجح
**الحل:**
```sql
-- إعادة حساب المتوسط المرجح
UPDATE أرصدة_المخزون SET 
    المتوسط_المرجح = إجمالي_القيمة / الكمية_المتاحة
WHERE الكمية_المتاحة > 0;
```

### مشكلة: عدم تطابق الأرصدة
**الحل:**
```sql
-- تشغيل استعلام التحقق من سلامة البيانات
-- (موجود في ملف اختبار_النظام_وبيانات_تجريبية.sql)
```

## 📞 الدعم الفني

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +20-xxx-xxx-xxxx
- **ساعات العمل**: الأحد - الخميس، 9 صباحاً - 5 مساءً

### قبل الاتصال بالدعم
1. راجع هذا الدليل والأسئلة الشائعة
2. جرب الحلول المقترحة لاستكشاف الأخطاء
3. اجمع معلومات الخطأ (رسائل الخطأ، لقطات الشاشة)
4. حدد الخطوات التي أدت للمشكلة

---

**تم إنشاء هذا النظام بعناية فائقة لضمان دقة إدارة المخزون والتكاليف في صناعة المعمول** 🥜✨
