-- =====================================================
-- إصلاح المشاكل في قاعدة البيانات
-- نظام إدارة تصنيع المعمول بالجوز
-- =====================================================

-- =====================================================
-- إنشاء الفهارس بصيغة Access الصحيحة
-- =====================================================

-- فهارس جدول حركات المخزون
CREATE INDEX idx_حركات_المخزون_المادة ON حركات_المخزون (رقم_المادة);
CREATE INDEX idx_حركات_المخزون_المخزن ON حركات_المخزون (رقم_المخزن);
CREATE INDEX idx_حركات_المخزون_التاريخ ON حركات_المخزون (تاريخ_الحركة);
CREATE INDEX idx_حركات_المخزون_النوع ON حركات_المخزون (نوع_الحركة);

-- فهارس جدول أرصدة المخزون
CREATE INDEX idx_أرصدة_المخزون_المادة ON أرصدة_المخزون (رقم_المادة);
CREATE INDEX idx_أرصدة_المخزون_المخزن ON أرصدة_المخزون (رقم_المخزن);

-- فهارس جدول فواتير الشراء
CREATE INDEX idx_فواتير_الشراء_المورد ON فواتير_الشراء (رقم_المورد);
CREATE INDEX idx_فواتير_الشراء_التاريخ ON فواتير_الشراء (تاريخ_الفاتورة);
CREATE INDEX idx_فواتير_الشراء_الحالة ON فواتير_الشراء (حالة_الفاتورة);

-- فهارس جدول أوامر الإنتاج
CREATE INDEX idx_أوامر_الإنتاج_المنتج ON أوامر_الإنتاج (رقم_المنتج);
CREATE INDEX idx_أوامر_الإنتاج_التاريخ ON أوامر_الإنتاج (تاريخ_الأمر);
CREATE INDEX idx_أوامر_الإنتاج_الحالة ON أوامر_الإنتاج (حالة_الأمر);

-- =====================================================
-- إنشاء العلاقات بين الجداول
-- =====================================================

-- العلاقات الأساسية
ALTER TABLE المواد_الخام ADD CONSTRAINT FK_المواد_وحدة_القياس 
    FOREIGN KEY (رقم_وحدة_القياس) REFERENCES وحدات_القياس(رقم_الوحدة);

ALTER TABLE المواد_الخام ADD CONSTRAINT FK_المواد_المورد_الافتراضي 
    FOREIGN KEY (رقم_المورد_الافتراضي) REFERENCES الموردين(رقم_المورد);

ALTER TABLE المنتجات_التامة ADD CONSTRAINT FK_المنتجات_وحدة_القياس 
    FOREIGN KEY (رقم_وحدة_القياس) REFERENCES وحدات_القياس(رقم_الوحدة);

-- علاقات فواتير الشراء
ALTER TABLE فواتير_الشراء ADD CONSTRAINT FK_فواتير_الشراء_المورد 
    FOREIGN KEY (رقم_المورد) REFERENCES الموردين(رقم_المورد);

ALTER TABLE فواتير_الشراء ADD CONSTRAINT FK_فواتير_الشراء_المخزن 
    FOREIGN KEY (رقم_المخزن) REFERENCES المخازن(رقم_المخزن);

ALTER TABLE تفاصيل_فواتير_الشراء ADD CONSTRAINT FK_تفاصيل_فواتير_الفاتورة 
    FOREIGN KEY (رقم_الفاتورة) REFERENCES فواتير_الشراء(رقم_الفاتورة);

ALTER TABLE تفاصيل_فواتير_الشراء ADD CONSTRAINT FK_تفاصيل_فواتير_المادة 
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة);

-- علاقات الوصفات
ALTER TABLE الوصفات ADD CONSTRAINT FK_الوصفات_المنتج 
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات_التامة(رقم_المنتج);

ALTER TABLE الوصفات ADD CONSTRAINT FK_الوصفات_وحدة_الإنتاج 
    FOREIGN KEY (رقم_وحدة_الإنتاج) REFERENCES وحدات_القياس(رقم_الوحدة);

ALTER TABLE تفاصيل_الوصفات ADD CONSTRAINT FK_تفاصيل_الوصفات_الوصفة 
    FOREIGN KEY (رقم_الوصفة) REFERENCES الوصفات(رقم_الوصفة);

ALTER TABLE تفاصيل_الوصفات ADD CONSTRAINT FK_تفاصيل_الوصفات_المادة 
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة);

ALTER TABLE تفاصيل_الوصفات ADD CONSTRAINT FK_تفاصيل_الوصفات_وحدة_القياس 
    FOREIGN KEY (رقم_وحدة_القياس) REFERENCES وحدات_القياس(رقم_الوحدة);

-- علاقات أوامر الإنتاج
ALTER TABLE أوامر_الإنتاج ADD CONSTRAINT FK_أوامر_الإنتاج_المنتج 
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات_التامة(رقم_المنتج);

ALTER TABLE أوامر_الإنتاج ADD CONSTRAINT FK_أوامر_الإنتاج_الوصفة 
    FOREIGN KEY (رقم_الوصفة) REFERENCES الوصفات(رقم_الوصفة);

ALTER TABLE أوامر_الإنتاج ADD CONSTRAINT FK_أوامر_الإنتاج_مركز_التكلفة 
    FOREIGN KEY (رقم_مركز_التكلفة) REFERENCES مراكز_التكلفة(رقم_مركز_التكلفة);

ALTER TABLE أوامر_الإنتاج ADD CONSTRAINT FK_أوامر_الإنتاج_المخزن 
    FOREIGN KEY (رقم_المخزن_الإنتاج) REFERENCES المخازن(رقم_المخزن);

-- علاقات حركات المخزون
ALTER TABLE حركات_المخزون ADD CONSTRAINT FK_حركات_المخزون_المادة 
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة);

ALTER TABLE حركات_المخزون ADD CONSTRAINT FK_حركات_المخزون_المخزن 
    FOREIGN KEY (رقم_المخزن) REFERENCES المخازن(رقم_المخزن);

-- علاقات أرصدة المخزون
ALTER TABLE أرصدة_المخزون ADD CONSTRAINT FK_أرصدة_المخزون_المادة 
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الخام(رقم_المادة);

ALTER TABLE أرصدة_المخزون ADD CONSTRAINT FK_أرصدة_المخزون_المخزن 
    FOREIGN KEY (رقم_المخزن) REFERENCES المخازن(رقم_المخزن);

-- علاقات التكاليف
ALTER TABLE تكاليف_الأيدي_العاملة ADD CONSTRAINT FK_تكاليف_الأيدي_العاملة_أمر_الإنتاج 
    FOREIGN KEY (رقم_أمر_الإنتاج) REFERENCES أوامر_الإنتاج(رقم_أمر_الإنتاج);

ALTER TABLE التكاليف_المباشرة ADD CONSTRAINT FK_التكاليف_المباشرة_أمر_الإنتاج 
    FOREIGN KEY (رقم_أمر_الإنتاج) REFERENCES أوامر_الإنتاج(رقم_أمر_الإنتاج);

ALTER TABLE التكاليف_غير_المباشرة ADD CONSTRAINT FK_التكاليف_غير_المباشرة_مركز_التكلفة 
    FOREIGN KEY (رقم_مركز_التكلفة) REFERENCES مراكز_التكلفة(رقم_مركز_التكلفة);

ALTER TABLE توزيع_التكاليف_غير_المباشرة ADD CONSTRAINT FK_توزيع_التكاليف_التكلفة 
    FOREIGN KEY (رقم_التكلفة) REFERENCES التكاليف_غير_المباشرة(رقم_التكلفة);

ALTER TABLE توزيع_التكاليف_غير_المباشرة ADD CONSTRAINT FK_توزيع_التكاليف_أمر_الإنتاج 
    FOREIGN KEY (رقم_أمر_الإنتاج) REFERENCES أوامر_الإنتاج(رقم_أمر_الإنتاج);

-- علاقات المبيعات (اختيارية)
ALTER TABLE فواتير_المبيعات ADD CONSTRAINT FK_فواتير_المبيعات_العميل 
    FOREIGN KEY (رقم_العميل) REFERENCES العملاء(رقم_العميل);

ALTER TABLE فواتير_المبيعات ADD CONSTRAINT FK_فواتير_المبيعات_المخزن 
    FOREIGN KEY (رقم_المخزن) REFERENCES المخازن(رقم_المخزن);

ALTER TABLE تفاصيل_فواتير_المبيعات ADD CONSTRAINT FK_تفاصيل_المبيعات_الفاتورة 
    FOREIGN KEY (رقم_فاتورة_المبيعات) REFERENCES فواتير_المبيعات(رقم_فاتورة_المبيعات);

ALTER TABLE تفاصيل_فواتير_المبيعات ADD CONSTRAINT FK_تفاصيل_المبيعات_المنتج 
    FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات_التامة(رقم_المنتج);

-- =====================================================
-- إنشاء قيود التحقق من صحة البيانات
-- =====================================================

-- قيود الكميات (يجب أن تكون موجبة)
ALTER TABLE المواد_الخام ADD CONSTRAINT CHK_المواد_الحد_الأدنى 
    CHECK (الحد_الأدنى >= 0);

ALTER TABLE المواد_الخام ADD CONSTRAINT CHK_المواد_نقطة_إعادة_الطلب 
    CHECK (نقطة_إعادة_الطلب >= 0);

ALTER TABLE تفاصيل_فواتير_الشراء ADD CONSTRAINT CHK_تفاصيل_فواتير_الكمية 
    CHECK (الكمية > 0);

ALTER TABLE تفاصيل_فواتير_الشراء ADD CONSTRAINT CHK_تفاصيل_فواتير_السعر 
    CHECK (سعر_الوحدة > 0);

ALTER TABLE تفاصيل_الوصفات ADD CONSTRAINT CHK_تفاصيل_الوصفات_الكمية 
    CHECK (الكمية_المطلوبة > 0);

ALTER TABLE أوامر_الإنتاج ADD CONSTRAINT CHK_أوامر_الإنتاج_الكمية 
    CHECK (الكمية_المطلوبة > 0);

ALTER TABLE حركات_المخزون ADD CONSTRAINT CHK_حركات_المخزون_الكمية 
    CHECK (الكمية > 0);

-- قيود حالات الفواتير وأوامر الإنتاج
ALTER TABLE فواتير_الشراء ADD CONSTRAINT CHK_فواتير_الشراء_الحالة 
    CHECK (حالة_الفاتورة IN ('مسودة', 'معتمدة', 'ملغاة'));

ALTER TABLE أوامر_الإنتاج ADD CONSTRAINT CHK_أوامر_الإنتاج_الحالة 
    CHECK (حالة_الأمر IN ('مخطط', 'قيد التنفيذ', 'مكتمل', 'ملغى'));

-- قيود نوع حركات المخزون
ALTER TABLE حركات_المخزون ADD CONSTRAINT CHK_حركات_المخزون_النوع 
    CHECK (نوع_الحركة IN ('دخول', 'خروج', 'تحويل', 'جرد', 'تسوية'));

-- قيود مصدر حركات المخزون
ALTER TABLE حركات_المخزون ADD CONSTRAINT CHK_حركات_المخزون_المصدر 
    CHECK (مصدر_الحركة IN ('فاتورة_شراء', 'أمر_إنتاج', 'مبيعات', 'تحويل', 'جرد', 'تسوية'));

-- =====================================================
-- إنشاء مشغلات (Triggers) لتحديث البيانات تلقائياً
-- =====================================================

-- مشغل لتحديث إجمالي فاتورة الشراء عند إضافة أو تعديل التفاصيل
-- ملاحظة: Access لا يدعم المشغلات بنفس طريقة SQL Server
-- سيتم تنفيذ هذا من خلال كود VBA في النماذج

-- =====================================================
-- إنشاء استعلامات محفوظة للعمليات الشائعة
-- =====================================================

-- استعلام للحصول على المواد الناقصة
CREATE VIEW المواد_الناقصة AS
SELECT 
    م.كود_المادة,
    م.اسم_المادة,
    ر.الكمية_المتاحة,
    م.الحد_الأدنى,
    (م.الحد_الأدنى - ر.الكمية_المتاحة) AS الكمية_المطلوبة
FROM المواد_الخام م
LEFT JOIN أرصدة_المخزون ر ON م.رقم_المادة = ر.رقم_المادة
WHERE COALESCE(ر.الكمية_المتاحة, 0) < م.الحد_الأدنى;

-- استعلام لحساب تكلفة الوصفة بالأسعار الحالية
CREATE VIEW تكلفة_الوصفات_الحالية AS
SELECT 
    و.رقم_الوصفة,
    و.اسم_الوصفة,
    م.اسم_المنتج,
    SUM(ت.الكمية_المطلوبة * COALESCE(ر.المتوسط_المرجح, 0)) AS تكلفة_المواد_الحالية,
    و.كمية_الإنتاج,
    (SUM(ت.الكمية_المطلوبة * COALESCE(ر.المتوسط_المرجح, 0)) / و.كمية_الإنتاج) AS تكلفة_الوحدة_الحالية
FROM الوصفات و
INNER JOIN المنتجات_التامة م ON و.رقم_المنتج = م.رقم_المنتج
INNER JOIN تفاصيل_الوصفات ت ON و.رقم_الوصفة = ت.رقم_الوصفة
LEFT JOIN أرصدة_المخزون ر ON ت.رقم_المادة = ر.رقم_المادة
WHERE و.نشطة = True
GROUP BY و.رقم_الوصفة, و.اسم_الوصفة, م.اسم_المنتج, و.كمية_الإنتاج;

-- استعلام لمتابعة أوامر الإنتاج النشطة
CREATE VIEW أوامر_الإنتاج_النشطة AS
SELECT 
    أ.رقم_أمر_الإنتاج,
    م.اسم_المنتج,
    أ.الكمية_المطلوبة,
    أ.الكمية_المنتجة_فعلياً,
    أ.حالة_الأمر,
    أ.تاريخ_البدء_المخطط,
    أ.تاريخ_الانتهاء_المخطط,
    أ.تاريخ_البدء_الفعلي,
    أ.تاريخ_الانتهاء_الفعلي,
    مك.اسم_مركز_التكلفة,
    DATEDIFF('d', أ.تاريخ_البدء_المخطط, Date()) AS أيام_التأخير
FROM أوامر_الإنتاج أ
INNER JOIN المنتجات_التامة م ON أ.رقم_المنتج = م.رقم_المنتج
LEFT JOIN مراكز_التكلفة مك ON أ.رقم_مركز_التكلفة = مك.رقم_مركز_التكلفة
WHERE أ.حالة_الأمر IN ('مخطط', 'قيد التنفيذ');

-- =====================================================
-- إنشاء استعلامات للتقارير السريعة
-- =====================================================

-- تقرير سريع للمخزون
CREATE VIEW تقرير_المخزون_السريع AS
SELECT 
    م.مجموعة_المادة,
    COUNT(*) AS عدد_المواد,
    SUM(ر.الكمية_المتاحة) AS إجمالي_الكمية,
    SUM(ر.إجمالي_القيمة) AS إجمالي_القيمة,
    COUNT(CASE WHEN ر.الكمية_المتاحة <= م.الحد_الأدنى THEN 1 END) AS المواد_تحت_الحد_الأدنى
FROM المواد_الخام م
LEFT JOIN أرصدة_المخزون ر ON م.رقم_المادة = ر.رقم_المادة
GROUP BY م.مجموعة_المادة;

-- تقرير سريع للإنتاج
CREATE VIEW تقرير_الإنتاج_السريع AS
SELECT 
    م.مجموعة_المنتج,
    COUNT(*) AS عدد_أوامر_الإنتاج,
    SUM(أ.الكمية_المطلوبة) AS إجمالي_الكمية_المطلوبة,
    SUM(أ.الكمية_المنتجة_فعلياً) AS إجمالي_الكمية_المنتجة,
    SUM(أ.إجمالي_التكلفة) AS إجمالي_التكلفة,
    AVG(أ.تكلفة_الوحدة) AS متوسط_تكلفة_الوحدة
FROM أوامر_الإنتاج أ
INNER JOIN المنتجات_التامة م ON أ.رقم_المنتج = م.رقم_المنتج
WHERE أ.حالة_الأمر = 'مكتمل'
GROUP BY م.مجموعة_المنتج;
