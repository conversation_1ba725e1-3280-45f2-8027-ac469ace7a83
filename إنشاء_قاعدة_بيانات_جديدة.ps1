# إنشاء قاعدة بيانات جديدة من الصفر
Write-Host "إنشاء قاعدة بيانات جديدة..." -ForegroundColor Green

# حذف قاعدة البيانات القديمة إذا كانت موجودة
$dbPath = "نظام_إدارة_تصنيع_المعمول_جديد.accdb"
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force
    Write-Host "تم حذف قاعدة البيانات القديمة" -ForegroundColor Yellow
}

try {
    # إنشاء قاعدة بيانات جديدة
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    $access.NewCurrentDatabase($dbPath)
    
    Write-Host "تم إنشاء قاعدة البيانات الجديدة: $dbPath" -ForegroundColor Green
    
    # إنشاء الجداول الأساسية
    Write-Host "إنشاء الجداول الأساسية..." -ForegroundColor Yellow
    
    # جدول وحدات القياس
    $access.DoCmd.RunSQL("CREATE TABLE وحدات_القياس (
        رقم_الوحدة AUTOINCREMENT PRIMARY KEY,
        اسم_الوحدة TEXT(50) NOT NULL,
        رمز_الوحدة TEXT(10) NOT NULL,
        نوع_الوحدة TEXT(20),
        معامل_التحويل DOUBLE DEFAULT 1,
        وحدة_أساسية YESNO DEFAULT False,
        ملاحظات MEMO,
        نشطة YESNO DEFAULT True
    )")
    
    # جدول المخازن
    $access.DoCmd.RunSQL("CREATE TABLE المخازن (
        رقم_المخزن AUTOINCREMENT PRIMARY KEY,
        اسم_المخزن TEXT(100) NOT NULL,
        نوع_المخزن TEXT(50),
        موقع_المخزن TEXT(200),
        مسؤول_المخزن TEXT(100),
        سعة_المخزن DOUBLE,
        ملاحظات MEMO,
        نشط YESNO DEFAULT True
    )")
    
    # جدول الموردين
    $access.DoCmd.RunSQL("CREATE TABLE الموردين (
        رقم_المورد AUTOINCREMENT PRIMARY KEY,
        اسم_المورد TEXT(100) NOT NULL,
        اسم_جهة_الاتصال TEXT(100),
        عنوان_المورد MEMO,
        هاتف_المورد TEXT(50),
        ايميل_المورد TEXT(100),
        شروط_الدفع TEXT(100),
        حد_الائتمان CURRENCY,
        تقييم_المورد INTEGER,
        ملاحظات MEMO,
        نشط YESNO DEFAULT True
    )")
    
    # جدول المواد الخام
    $access.DoCmd.RunSQL("CREATE TABLE المواد_الخام (
        رقم_المادة AUTOINCREMENT PRIMARY KEY,
        كود_المادة TEXT(50) NOT NULL,
        اسم_المادة TEXT(100) NOT NULL,
        مجموعة_المادة TEXT(50),
        رقم_وحدة_القياس INTEGER,
        رقم_المورد_الافتراضي INTEGER,
        الحد_الأدنى DOUBLE DEFAULT 0,
        نقطة_إعادة_الطلب DOUBLE DEFAULT 0,
        متوسط_فترة_التوريد INTEGER DEFAULT 7,
        ملاحظات MEMO,
        نشط YESNO DEFAULT True
    )")
    
    # جدول المنتجات التامة
    $access.DoCmd.RunSQL("CREATE TABLE المنتجات_التامة (
        رقم_المنتج AUTOINCREMENT PRIMARY KEY,
        كود_المنتج TEXT(50) NOT NULL,
        اسم_المنتج TEXT(100) NOT NULL,
        مجموعة_المنتج TEXT(50),
        رقم_وحدة_القياس INTEGER,
        سعر_البيع CURRENCY,
        التكلفة_المعيارية CURRENCY,
        مدة_الصلاحية INTEGER,
        ملاحظات MEMO,
        نشط YESNO DEFAULT True
    )")
    
    # جدول فواتير الشراء
    $access.DoCmd.RunSQL("CREATE TABLE فواتير_الشراء (
        رقم_الفاتورة AUTOINCREMENT PRIMARY KEY,
        رقم_فاتورة_المورد TEXT(50) NOT NULL,
        رقم_المورد INTEGER NOT NULL,
        تاريخ_الفاتورة DATETIME NOT NULL,
        تاريخ_الاستحقاق DATETIME,
        رقم_المخزن INTEGER NOT NULL,
        إجمالي_الفاتورة CURRENCY DEFAULT 0,
        نسبة_الخصم DOUBLE DEFAULT 0,
        قيمة_الخصم CURRENCY DEFAULT 0,
        نسبة_الضريبة DOUBLE DEFAULT 0,
        قيمة_الضريبة CURRENCY DEFAULT 0,
        الصافي_بعد_الخصم CURRENCY DEFAULT 0,
        إجمالي_شامل_الضريبة CURRENCY DEFAULT 0,
        حالة_الفاتورة TEXT(20) DEFAULT 'مسودة',
        ملاحظات MEMO,
        مستخدم_الإدخال TEXT(50),
        تاريخ_الإدخال DATETIME DEFAULT Now()
    )")
    
    # جدول تفاصيل فواتير الشراء
    $access.DoCmd.RunSQL("CREATE TABLE تفاصيل_فواتير_الشراء (
        رقم_التفصيل AUTOINCREMENT PRIMARY KEY,
        رقم_الفاتورة INTEGER NOT NULL,
        رقم_المادة INTEGER NOT NULL,
        الكمية DOUBLE NOT NULL,
        سعر_الوحدة CURRENCY NOT NULL,
        إجمالي_السطر CURRENCY,
        نسبة_الخصم DOUBLE DEFAULT 0,
        قيمة_الخصم CURRENCY DEFAULT 0,
        الصافي_بعد_الخصم CURRENCY,
        تاريخ_الصلاحية DATETIME,
        رقم_اللوط TEXT(50),
        ملاحظات MEMO
    )")
    
    # جدول أرصدة المخزون
    $access.DoCmd.RunSQL("CREATE TABLE أرصدة_المخزون (
        رقم_الرصيد AUTOINCREMENT PRIMARY KEY,
        رقم_المادة INTEGER NOT NULL,
        رقم_المخزن INTEGER NOT NULL,
        الكمية_المتاحة DOUBLE DEFAULT 0,
        الكمية_المحجوزة DOUBLE DEFAULT 0,
        الكمية_الصافية DOUBLE DEFAULT 0,
        المتوسط_المرجح CURRENCY DEFAULT 0,
        إجمالي_القيمة CURRENCY DEFAULT 0,
        تاريخ_آخر_حركة DATETIME
    )")
    
    # جدول حركات المخزون
    $access.DoCmd.RunSQL("CREATE TABLE حركات_المخزون (
        رقم_الحركة AUTOINCREMENT PRIMARY KEY,
        رقم_المادة INTEGER NOT NULL,
        رقم_المخزن INTEGER NOT NULL,
        نوع_الحركة TEXT(20) NOT NULL,
        مصدر_الحركة TEXT(30),
        رقم_المصدر INTEGER,
        الكمية DOUBLE NOT NULL,
        سعر_الوحدة CURRENCY,
        إجمالي_القيمة CURRENCY,
        الرصيد_قبل_الحركة DOUBLE,
        الرصيد_بعد_الحركة DOUBLE,
        المتوسط_المرجح_قبل CURRENCY,
        المتوسط_المرجح_بعد CURRENCY,
        تاريخ_الحركة DATETIME DEFAULT Now(),
        رقم_اللوط TEXT(50),
        ملاحظات MEMO,
        مستخدم_الإدخال TEXT(50)
    )")
    
    Write-Host "تم إنشاء الجداول الأساسية بنجاح" -ForegroundColor Green
    
    # إدخال البيانات الأساسية
    Write-Host "إدخال البيانات الأساسية..." -ForegroundColor Yellow
    
    # وحدات القياس
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, وحدة_أساسية) VALUES ('كيلو جرام', 'كجم', 'وزن', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, معامل_التحويل) VALUES ('جرام', 'جم', 'وزن', 0.001)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, وحدة_أساسية) VALUES ('لتر', 'لتر', 'حجم', True)")
    $access.DoCmd.RunSQL("INSERT INTO وحدات_القياس (اسم_الوحدة, رمز_الوحدة, نوع_الوحدة, وحدة_أساسية) VALUES ('قطعة', 'قطعة', 'عدد', True)")
    
    # المخازن
    $access.DoCmd.RunSQL("INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, موقع_المخزن) VALUES ('مخزن المواد الخام', 'مواد خام', 'الدور الأرضي - قسم أ')")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, موقع_المخزن) VALUES ('مخزن المنتجات التامة', 'منتجات تامة', 'الدور الأرضي - قسم ب')")
    $access.DoCmd.RunSQL("INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, موقع_المخزن) VALUES ('مخزن الإنتاج', 'إنتاج', 'الدور الأول')")
    
    # الموردين
    $access.DoCmd.RunSQL("INSERT INTO الموردين (اسم_المورد, اسم_جهة_الاتصال, هاتف_المورد, شروط_الدفع) VALUES ('شركة الدقيق المصري', 'أحمد محمد', '02-12345678', 'آجل 30 يوم')")
    $access.DoCmd.RunSQL("INSERT INTO الموردين (اسم_المورد, اسم_جهة_الاتصال, هاتف_المورد, شروط_الدفع) VALUES ('مؤسسة المكسرات الذهبية', 'فاطمة حسن', '02-87654321', 'نقدي')")
    
    # المواد الخام
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, مجموعة_المادة, رقم_وحدة_القياس, الحد_الأدنى) VALUES ('DQ001', 'دقيق أبيض', 'دقيق', 1, 50)")
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, مجموعة_المادة, رقم_وحدة_القياس, الحد_الأدنى) VALUES ('SG001', 'سكر أبيض', 'سكر', 1, 25)")
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, مجموعة_المادة, رقم_وحدة_القياس, الحد_الأدنى) VALUES ('ZB001', 'زبدة', 'دهون', 1, 10)")
    $access.DoCmd.RunSQL("INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, مجموعة_المادة, رقم_وحدة_القياس, الحد_الأدنى) VALUES ('JZ001', 'جوز مقشر', 'مكسرات', 1, 15)")
    
    # المنتجات التامة
    $access.DoCmd.RunSQL("INSERT INTO المنتجات_التامة (كود_المنتج, اسم_المنتج, مجموعة_المنتج, رقم_وحدة_القياس, سعر_البيع) VALUES ('MJ001', 'معمول جوز صغير', 'معمول', 4, 5.50)")
    $access.DoCmd.RunSQL("INSERT INTO المنتجات_التامة (كود_المنتج, اسم_المنتج, مجموعة_المنتج, رقم_وحدة_القياس, سعر_البيع) VALUES ('MJ002', 'معمول جوز كبير', 'معمول', 4, 8.00)")
    
    Write-Host "تم إدخال البيانات الأساسية بنجاح" -ForegroundColor Green
    
    # إنشاء الاستعلامات الأساسية
    Write-Host "إنشاء الاستعلامات الأساسية..." -ForegroundColor Yellow
    
    # استعلام تقرير المخزون الحالي
    $access.DoCmd.RunSQL("CREATE VIEW تقرير_المخزون_الحالي AS
    SELECT 
        م.كود_المادة,
        م.اسم_المادة,
        مخ.اسم_المخزن,
        ر.الكمية_المتاحة,
        و.رمز_الوحدة,
        ر.المتوسط_المرجح,
        ر.إجمالي_القيمة,
        م.الحد_الأدنى,
        IIf(ر.الكمية_المتاحة <= م.الحد_الأدنى, 'تحت الحد الأدنى', 'طبيعي') AS حالة_المخزون
    FROM ((المواد_الخام م 
    LEFT JOIN أرصدة_المخزون ر ON م.رقم_المادة = ر.رقم_المادة)
    LEFT JOIN المخازن مخ ON ر.رقم_المخزن = مخ.رقم_المخزن)
    LEFT JOIN وحدات_القياس و ON م.رقم_وحدة_القياس = و.رقم_الوحدة
    WHERE م.نشط = True")
    
    # استعلام حركات المخزون
    $access.DoCmd.RunSQL("CREATE VIEW تقرير_حركات_المخزون AS
    SELECT 
        ح.تاريخ_الحركة,
        م.كود_المادة,
        م.اسم_المادة,
        مخ.اسم_المخزن,
        ح.نوع_الحركة,
        ح.مصدر_الحركة,
        ح.الكمية,
        ح.سعر_الوحدة,
        ح.إجمالي_القيمة,
        ح.الرصيد_بعد_الحركة,
        ح.المتوسط_المرجح_بعد,
        ح.ملاحظات
    FROM (حركات_المخزون ح 
    INNER JOIN المواد_الخام م ON ح.رقم_المادة = م.رقم_المادة)
    INNER JOIN المخازن مخ ON ح.رقم_المخزن = مخ.رقم_المخزن
    ORDER BY ح.تاريخ_الحركة DESC")
    
    Write-Host "تم إنشاء الاستعلامات الأساسية بنجاح" -ForegroundColor Green
    
    # حفظ وإغلاق قاعدة البيانات
    $access.CloseCurrentDatabase()
    $access.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    
    Write-Host "`n=== تم إنشاء قاعدة البيانات بنجاح ===" -ForegroundColor Green
    Write-Host "اسم الملف: $dbPath" -ForegroundColor Yellow
    Write-Host "الجداول المُنشأة:" -ForegroundColor Cyan
    Write-Host "  ✓ وحدات_القياس (4 وحدات)" -ForegroundColor White
    Write-Host "  ✓ المخازن (3 مخازن)" -ForegroundColor White
    Write-Host "  ✓ الموردين (2 موردين)" -ForegroundColor White
    Write-Host "  ✓ المواد_الخام (4 مواد)" -ForegroundColor White
    Write-Host "  ✓ المنتجات_التامة (2 منتجات)" -ForegroundColor White
    Write-Host "  ✓ فواتير_الشراء" -ForegroundColor White
    Write-Host "  ✓ تفاصيل_فواتير_الشراء" -ForegroundColor White
    Write-Host "  ✓ أرصدة_المخزون" -ForegroundColor White
    Write-Host "  ✓ حركات_المخزون" -ForegroundColor White
    Write-Host "`nالاستعلامات المُنشأة:" -ForegroundColor Cyan
    Write-Host "  ✓ تقرير_المخزون_الحالي" -ForegroundColor White
    Write-Host "  ✓ تقرير_حركات_المخزون" -ForegroundColor White
    
    # فتح قاعدة البيانات
    Write-Host "`nفتح قاعدة البيانات..." -ForegroundColor Green
    Start-Process "msaccess.exe" -ArgumentList "`"$dbPath`""
    
} catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
    if ($access) {
        try {
            $access.CloseCurrentDatabase()
            $access.Quit()
        } catch {}
    }
}

Write-Host "`nانتهى إنشاء قاعدة البيانات!" -ForegroundColor Green
