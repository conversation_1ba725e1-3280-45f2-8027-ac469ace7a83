' =====================================================
' وحدات VBA لنظام إدارة المخزون والتكاليف
' نظام إدارة تصنيع المعمول بالجوز
' =====================================================

' وحدة حساب المتوسط المرجح وتحديث المخزون
Option Compare Database
Option Explicit

' =====================================================
' دالة حساب المتوسط المرجح الجديد
' =====================================================
Public Function حساب_المتوسط_المرجح_الجديد(رقم_المادة As Long, رقم_المخزن As Long, _
                                        كمية_جديدة As Double, سعر_جديد As Currency) As Currency
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim الكمية_الحالية As Double
    Dim المتوسط_الحالي As Currency
    Dim القيمة_الحالية As Currency
    Dim القيمة_الجديدة As Currency
    Dim الكمية_الإجمالية As Double
    Dim القيمة_الإجمالية As Currency
    Dim المتوسط_الجديد As Currency
    
    Set db = CurrentDb()
    
    ' الحصول على الرصيد الحالي والمتوسط المرجح
    Set rs = db.OpenRecordset("SELECT الكمية_المتاحة, المتوسط_المرجح " & _
                             "FROM أرصدة_المخزون " & _
                             "WHERE رقم_المادة = " & رقم_المادة & _
                             " AND رقم_المخزن = " & رقم_المخزن)
    
    If rs.EOF Then
        ' إذا لم يكن هناك رصيد سابق
        الكمية_الحالية = 0
        المتوسط_الحالي = 0
    Else
        الكمية_الحالية = Nz(rs!الكمية_المتاحة, 0)
        المتوسط_الحالي = Nz(rs!المتوسط_المرجح, 0)
    End If
    
    rs.Close
    
    ' حساب المتوسط المرجح الجديد
    القيمة_الحالية = الكمية_الحالية * المتوسط_الحالي
    القيمة_الجديدة = كمية_جديدة * سعر_جديد
    الكمية_الإجمالية = الكمية_الحالية + كمية_جديدة
    القيمة_الإجمالية = القيمة_الحالية + القيمة_الجديدة
    
    If الكمية_الإجمالية > 0 Then
        المتوسط_الجديد = القيمة_الإجمالية / الكمية_الإجمالية
    Else
        المتوسط_الجديد = 0
    End If
    
    حساب_المتوسط_المرجح_الجديد = المتوسط_الجديد
    
    Set rs = Nothing
    Set db = Nothing
    
End Function

' =====================================================
' إجراء تحديث رصيد المخزون
' =====================================================
Public Sub تحديث_رصيد_المخزون(رقم_المادة As Long, رقم_المخزن As Long, _
                              كمية_التغيير As Double, سعر_الوحدة As Currency, _
                              نوع_الحركة As String)
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim الكمية_الجديدة As Double
    Dim المتوسط_الجديد As Currency
    Dim القيمة_الجديدة As Currency
    Dim sql As String
    
    Set db = CurrentDb()
    
    ' التحقق من وجود رصيد للمادة في المخزن
    sql = "SELECT * FROM أرصدة_المخزون " & _
          "WHERE رقم_المادة = " & رقم_المادة & _
          " AND رقم_المخزن = " & رقم_المخزن
    
    Set rs = db.OpenRecordset(sql)
    
    If rs.EOF Then
        ' إنشاء رصيد جديد
        rs.AddNew
        rs!رقم_المادة = رقم_المادة
        rs!رقم_المخزن = رقم_المخزن
        rs!الكمية_المتاحة = 0
        rs!المتوسط_المرجح = 0
        rs!إجمالي_القيمة = 0
    Else
        rs.Edit
    End If
    
    ' حساب الكمية الجديدة
    If نوع_الحركة = "دخول" Then
        الكمية_الجديدة = Nz(rs!الكمية_المتاحة, 0) + كمية_التغيير
        ' حساب المتوسط المرجح الجديد للدخول فقط
        المتوسط_الجديد = حساب_المتوسط_المرجح_الجديد(رقم_المادة, رقم_المخزن, كمية_التغيير, سعر_الوحدة)
    Else
        الكمية_الجديدة = Nz(rs!الكمية_المتاحة, 0) - كمية_التغيير
        ' للخروج نستخدم المتوسط المرجح الحالي
        المتوسط_الجديد = Nz(rs!المتوسط_المرجح, 0)
    End If
    
    ' التأكد من عدم وجود رصيد سالب
    If الكمية_الجديدة < 0 And نوع_الحركة = "خروج" Then
        MsgBox "تحذير: الكمية المطلوب صرفها أكبر من الرصيد المتاح!" & vbCrLf & _
               "الرصيد المتاح: " & Nz(rs!الكمية_المتاحة, 0) & vbCrLf & _
               "الكمية المطلوبة: " & كمية_التغيير, vbExclamation
        rs.CancelUpdate
        Exit Sub
    End If
    
    ' تحديث البيانات
    rs!الكمية_المتاحة = الكمية_الجديدة
    rs!المتوسط_المرجح = المتوسط_الجديد
    rs!إجمالي_القيمة = الكمية_الجديدة * المتوسط_الجديد
    rs!تاريخ_آخر_تحديث = Now()
    
    rs.Update
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
    
End Sub

' =====================================================
' إجراء إضافة حركة مخزون
' =====================================================
Public Sub إضافة_حركة_مخزون(رقم_المادة As Long, رقم_المخزن As Long, _
                            نوع_الحركة As String, مصدر_الحركة As String, _
                            رقم_المصدر As Long, الكمية As Double, _
                            سعر_الوحدة As Currency, Optional ملاحظات As String = "")
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim الرصيد_قبل As Double
    Dim المتوسط_قبل As Currency
    
    Set db = CurrentDb()
    
    ' الحصول على الرصيد قبل الحركة
    Set rs = db.OpenRecordset("SELECT الكمية_المتاحة, المتوسط_المرجح " & _
                             "FROM أرصدة_المخزون " & _
                             "WHERE رقم_المادة = " & رقم_المادة & _
                             " AND رقم_المخزن = " & رقم_المخزن)
    
    If rs.EOF Then
        الرصيد_قبل = 0
        المتوسط_قبل = 0
    Else
        الرصيد_قبل = Nz(rs!الكمية_المتاحة, 0)
        المتوسط_قبل = Nz(rs!المتوسط_المرجح, 0)
    End If
    rs.Close
    
    ' تحديث رصيد المخزون
    Call تحديث_رصيد_المخزون(رقم_المادة, رقم_المخزن, الكمية, سعر_الوحدة, نوع_الحركة)
    
    ' الحصول على الرصيد بعد الحركة
    Set rs = db.OpenRecordset("SELECT الكمية_المتاحة, المتوسط_المرجح " & _
                             "FROM أرصدة_المخزون " & _
                             "WHERE رقم_المادة = " & رقم_المادة & _
                             " AND رقم_المخزن = " & رقم_المخزن)
    
    Dim الرصيد_بعد As Double
    Dim المتوسط_بعد As Currency
    
    If Not rs.EOF Then
        الرصيد_بعد = Nz(rs!الكمية_المتاحة, 0)
        المتوسط_بعد = Nz(rs!المتوسط_المرجح, 0)
    End If
    rs.Close
    
    ' إضافة سجل الحركة
    Set rs = db.OpenRecordset("حركات_المخزون")
    rs.AddNew
    rs!رقم_المادة = رقم_المادة
    rs!رقم_المخزن = رقم_المخزن
    rs!نوع_الحركة = نوع_الحركة
    rs!مصدر_الحركة = مصدر_الحركة
    rs!رقم_المصدر = رقم_المصدر
    rs!الكمية = الكمية
    rs!سعر_الوحدة = سعر_الوحدة
    rs!إجمالي_القيمة = الكمية * سعر_الوحدة
    rs!الرصيد_قبل_الحركة = الرصيد_قبل
    rs!الرصيد_بعد_الحركة = الرصيد_بعد
    rs!المتوسط_المرجح_قبل = المتوسط_قبل
    rs!المتوسط_المرجح_بعد = المتوسط_بعد
    rs!تاريخ_الحركة = Now()
    rs!ملاحظات = ملاحظات
    rs!مستخدم_الإدخال = Environ("USERNAME")
    rs.Update
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
    
End Sub

' =====================================================
' إجراء معالجة فاتورة شراء
' =====================================================
Public Sub معالجة_فاتورة_شراء(رقم_الفاتورة As Long)
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim sql As String
    
    Set db = CurrentDb()
    
    ' الحصول على تفاصيل الفاتورة
    sql = "SELECT ف.رقم_المخزن, ت.رقم_المادة, ت.الكمية, ت.سعر_الوحدة " & _
          "FROM فواتير_الشراء ف INNER JOIN تفاصيل_فواتير_الشراء ت " & _
          "ON ف.رقم_الفاتورة = ت.رقم_الفاتورة " & _
          "WHERE ف.رقم_الفاتورة = " & رقم_الفاتورة & _
          " AND ف.حالة_الفاتورة = 'معتمدة'"
    
    Set rs = db.OpenRecordset(sql)
    
    If rs.EOF Then
        MsgBox "لا توجد فاتورة معتمدة بهذا الرقم أو لا توجد تفاصيل للفاتورة", vbExclamation
        Exit Sub
    End If
    
    ' معالجة كل صنف في الفاتورة
    Do While Not rs.EOF
        Call إضافة_حركة_مخزون(rs!رقم_المادة, rs!رقم_المخزن, "دخول", _
                               "فاتورة_شراء", رقم_الفاتورة, rs!الكمية, _
                               rs!سعر_الوحدة, "دخول من فاتورة شراء رقم " & رقم_الفاتورة)
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox "تم معالجة فاتورة الشراء رقم " & رقم_الفاتورة & " بنجاح", vbInformation
    
End Sub

' =====================================================
' إجراء معالجة أمر إنتاج - صرف المواد الخام
' =====================================================
Public Sub صرف_مواد_خام_لأمر_إنتاج(رقم_أمر_الإنتاج As Long)
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim sql As String
    
    Set db = CurrentDb()
    
    ' الحصول على تفاصيل الوصفة المطلوبة
    sql = "SELECT أ.رقم_المخزن_الإنتاج, أ.الكمية_المطلوبة, " & _
          "       و.كمية_الإنتاج, ت.رقم_المادة, ت.الكمية_المطلوبة " & _
          "FROM أوامر_الإنتاج أ " & _
          "INNER JOIN الوصفات و ON أ.رقم_الوصفة = و.رقم_الوصفة " & _
          "INNER JOIN تفاصيل_الوصفات ت ON و.رقم_الوصفة = ت.رقم_الوصفة " & _
          "WHERE أ.رقم_أمر_الإنتاج = " & رقم_أمر_الإنتاج & _
          " AND أ.حالة_الأمر = 'قيد التنفيذ'"
    
    Set rs = db.OpenRecordset(sql)
    
    If rs.EOF Then
        MsgBox "لا يوجد أمر إنتاج قيد التنفيذ بهذا الرقم", vbExclamation
        Exit Sub
    End If
    
    ' حساب نسبة التحويل
    Dim نسبة_التحويل As Double
    نسبة_التحويل = rs!الكمية_المطلوبة / rs!كمية_الإنتاج
    
    ' صرف كل مادة خام
    Do While Not rs.EOF
        Dim الكمية_المطلوبة As Double
        الكمية_المطلوبة = rs!الكمية_المطلوبة * نسبة_التحويل
        
        ' الحصول على المتوسط المرجح للمادة
        Dim المتوسط_المرجح As Currency
        Dim rsRصيد As DAO.Recordset
        Set rsRصيد = db.OpenRecordset("SELECT المتوسط_المرجح FROM أرصدة_المخزون " & _
                                     "WHERE رقم_المادة = " & rs!رقم_المادة & _
                                     " AND رقم_المخزن = " & rs!رقم_المخزن_الإنتاج)
        
        If Not rsRصيد.EOF Then
            المتوسط_المرجح = Nz(rsRصيد!المتوسط_المرجح, 0)
        Else
            المتوسط_المرجح = 0
        End If
        rsRصيد.Close
        
        Call إضافة_حركة_مخزون(rs!رقم_المادة, rs!رقم_المخزن_الإنتاج, "خروج", _
                               "أمر_إنتاج", رقم_أمر_الإنتاج, الكمية_المطلوبة, _
                               المتوسط_المرجح, "صرف لأمر إنتاج رقم " & رقم_أمر_الإنتاج)
        
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox "تم صرف المواد الخام لأمر الإنتاج رقم " & رقم_أمر_الإنتاج & " بنجاح", vbInformation
    
End Sub
